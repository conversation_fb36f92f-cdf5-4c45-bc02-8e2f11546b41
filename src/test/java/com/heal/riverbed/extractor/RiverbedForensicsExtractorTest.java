//package com.heal.riverbed.extractor;
//
//import com.appnomic.appsone.common.protbuf.CommandRequestProtos;
//import com.heal.etladapter.pojos.ForensicItem;
//import com.heal.etladapter.pojos.ForensicOutput;
//import com.heal.riverbed.constants.Constants;
//import com.heal.riverbed.exceptions.RiverbedConnectorException;
//import com.heal.riverbed.parser.XmlParser;
//import org.junit.Before;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.MockitoAnnotations;
//import org.mockito.junit.MockitoJUnitRunner;
//
//import java.util.*;
//
//import static org.junit.Assert.*;
//import static org.mockito.ArgumentMatchers.*;
//import static org.mockito.Mockito.*;
//
//@RunWith(MockitoJUnitRunner.class)
//public class RiverbedForensicsExtractorTest {
//
//    @InjectMocks
//    private RiverbedForensicsExtractor riverbedForensicsExtractor;
//
//    @Mock
//    private XmlParser xmlParser;
//
//    private Map<String, String> parameters;
//
//    @Before
//    public void setUp() {
//        MockitoAnnotations.openMocks(this);
//
//        // Setup test parameters
//        parameters = new HashMap<>();
//        parameters.put(Constants.RIVERBED_URL, "https://test-riverbed-url.com/api/forensics");
//        parameters.put(Constants.RIVERBED_FORENSICS_REQUEST, "{\"test\":\"forensics_payload\"}");
//        parameters.put(Constants.RIVERBED_AUTHORIZATION, "Bearer test-token");
//        parameters.put(Constants.RIVERBED_ENTITY_TYPES, "NETWORK_TRAFFIC,SECURITY_EVENTS");
//        parameters.put("domain", "riverbed");
//        parameters.put("forensics.types", "network_traffic,security_events");
//        parameters.put("supervisor.identifier", "test-supervisor");
//        parameters.put("agent.identifier", "test-agent");
//        parameters.put("agent.type", "RIVERBED_FORENSICS");
//        parameters.put("trigger.source", "SCHEDULED");
//        parameters.put("forensics_network_traffic_limit", "1000");
//        parameters.put("forensics_network_traffic_resolution", "1min");
//        parameters.put("forensics_network_traffic_filters", "protocol=TCP");
//
//        // Set up the extractor with test data
//        riverbedForensicsExtractor.parameters = parameters;
//        riverbedForensicsExtractor.jobId = "test-job-123";
//        riverbedForensicsExtractor.connectorInstanceIdentifier = "test-instance";
//        riverbedForensicsExtractor.className = "RiverbedForensicsExtractor";
//    }
//
//    @Test
//    public void testInitializeSuccess() throws Exception {
//        // Test initialization
//        riverbedForensicsExtractor.initialize();
//
//        // Verify that initialization completed without exceptions
//        // No specific assertions needed as we're testing that no exception is thrown
//    }
//
//    @Test(expected = RiverbedConnectorException.class)
//    public void testInitializeFailsWithMissingUrl() throws Exception {
//        // Remove URL parameter
//        parameters.remove(Constants.RIVERBED_URL);
//        riverbedForensicsExtractor.parameters = parameters;
//
//        // Test initialization - should throw exception
//        riverbedForensicsExtractor.initialize();
//    }
//
//    @Test(expected = RiverbedConnectorException.class)
//    public void testInitializeFailsWithMissingForensicsRequest() throws Exception {
//        // Remove forensics request parameter
//        parameters.remove(Constants.RIVERBED_FORENSICS_REQUEST);
//        riverbedForensicsExtractor.parameters = parameters;
//
//        // Test initialization - should throw exception
//        riverbedForensicsExtractor.initialize();
//    }
//
//    @Test(expected = RiverbedConnectorException.class)
//    public void testInitializeFailsWithMissingAuthorization() throws Exception {
//        // Remove authorization parameter
//        parameters.remove(Constants.RIVERBED_AUTHORIZATION);
//        riverbedForensicsExtractor.parameters = parameters;
//
//        // Test initialization - should throw exception
//        riverbedForensicsExtractor.initialize();
//    }
//
//    @Test(expected = RiverbedConnectorException.class)
//    public void testInitializeFailsWithMissingEntityTypes() throws Exception {
//        // Remove entity types parameter
//        parameters.remove(Constants.RIVERBED_ENTITY_TYPES);
//        riverbedForensicsExtractor.parameters = parameters;
//
//        // Test initialization - should throw exception
//        riverbedForensicsExtractor.initialize();
//    }
//
//    @Test
//    public void testExtractWithObjectParameter() throws Exception {
//        // Initialize the extractor first
//        riverbedForensicsExtractor.initialize();
//
//        // Test the extract method with Object parameter
//        ForensicOutput result = riverbedForensicsExtractor.extract(1000L, 2000L, new Object());
//
//        // Should return empty ForensicOutput
//        assertNotNull(result);
//        assertNotNull(result.getCommandRequest());
//        assertNotNull(result.getForensicItemList());
//        assertTrue(result.getForensicItemList().isEmpty());
//    }
//
//    @Test
//    public void testExtractWithEmptyForensicOutput() throws Exception {
//        // Initialize the extractor first
//        riverbedForensicsExtractor.initialize();
//
//        // Create empty ForensicOutput
//        ForensicOutput emptyOutput = ForensicOutput.builder()
//                .commandRequest(CommandRequestProtos.CommandRequest.newBuilder().build())
//                .forensicItemList(Collections.emptyList())
//                .build();
//
//        // Test extract with empty input
//        ForensicOutput result = riverbedForensicsExtractor.extract(1000L, 2000L, emptyOutput);
//
//        // Should return ForensicOutput with command request and empty list
//        assertNotNull(result);
//        assertNotNull(result.getCommandRequest());
//        assertNotNull(result.getForensicItemList());
//        assertTrue(result.getForensicItemList().isEmpty());
//    }
//
//    @Test
//    public void testExtractWithSuccessfulResponse() throws Exception {
//        // Initialize the extractor first
//        riverbedForensicsExtractor.initialize();
//
//        // Mock XML parser response
//        Map<String, Map<String, Object>> mockForensicsRecords = new HashMap<>();
//        Map<String, Object> forensicsData = new HashMap<>();
//        forensicsData.put("source_ip", "*************");
//        forensicsData.put("destination_ip", "*********");
//        forensicsData.put("source_port", 8080);
//        forensicsData.put("destination_port", 443);
//        forensicsData.put("protocol", "TCP");
//        forensicsData.put("application_name", "HTTPS");
//        forensicsData.put("bytes_transferred", 1024L);
//        mockForensicsRecords.put("session_1", forensicsData);
//
//        when(xmlParser.buildForensicsRecords(anyString(), anyString()))
//                .thenReturn(mockForensicsRecords);
//
//        // Mock HTTP connection
//        riverbedForensicsExtractor.httpConnection = mock(com.heal.etladapter.connections.HttpConnection.class);
//        when(riverbedForensicsExtractor.httpConnection.httpPost(anyString(), anyString(), anyMap(), anyString()))
//                .thenReturn("<xml>test response</xml>");
//
//        // Test extract
//        ForensicOutput result = riverbedForensicsExtractor.extract(1000L, 2000L, ForensicOutput.builder().build());
//
//        // Should return ForensicOutput with forensic items
//        assertNotNull(result);
//        assertNotNull(result.getCommandRequest());
//        assertNotNull(result.getForensicItemList());
//        assertFalse(result.getForensicItemList().isEmpty());
//
//        // Verify forensic item content
//        ForensicItem item = result.getForensicItemList().get(0);
//        assertNotNull(item);
//        assertEquals("session_1", item.getItemId());
//        assertEquals("network_traffic", item.getItemType());
//        assertNotNull(item.getData());
//        assertEquals("*************", item.getData().get("sourceIp"));
//        assertEquals("*********", item.getData().get("destinationIp"));
//    }
//
//    @Test
//    public void testExtractHandlesException() throws Exception {
//        // Initialize the extractor first
//        riverbedForensicsExtractor.initialize();
//
//        // Mock XML parser to throw exception
//        when(xmlParser.buildForensicsRecords(anyString(), anyString()))
//                .thenThrow(new RuntimeException("Test exception"));
//
//        // Mock HTTP connection
//        riverbedForensicsExtractor.httpConnection = mock(com.heal.etladapter.connections.HttpConnection.class);
//        when(riverbedForensicsExtractor.httpConnection.httpPost(anyString(), anyString(), anyMap(), anyString()))
//                .thenReturn("<xml>test response</xml>");
//
//        // Test extract - should handle exception gracefully
//        ForensicOutput result = riverbedForensicsExtractor.extract(1000L, 2000L, ForensicOutput.builder().build());
//
//        // Should return ForensicOutput with empty list when exception occurs
//        assertNotNull(result);
//        assertNotNull(result.getCommandRequest());
//        assertNotNull(result.getForensicItemList());
//        assertTrue(result.getForensicItemList().isEmpty());
//    }
//
//    @Test
//    public void testParameterDefaults() throws Exception {
//        // Initialize the extractor first
//        riverbedForensicsExtractor.initialize();
//
//        // Remove some optional parameters to test defaults
//        parameters.remove("forensics.types");
//        parameters.remove("supervisor.identifier");
//        parameters.remove("agent.identifier");
//        riverbedForensicsExtractor.parameters = parameters;
//
//        // Mock XML parser
//        when(xmlParser.buildForensicsRecords(anyString(), anyString()))
//                .thenReturn(new HashMap<>());
//
//        // Mock HTTP connection
//        riverbedForensicsExtractor.httpConnection = mock(com.heal.etladapter.connections.HttpConnection.class);
//        when(riverbedForensicsExtractor.httpConnection.httpPost(anyString(), anyString(), anyMap(), anyString()))
//                .thenReturn("<xml>test response</xml>");
//
//        // Test extract - should use default values
//        ForensicOutput result = riverbedForensicsExtractor.extract(1000L, 2000L, ForensicOutput.builder().build());
//
//        // Should not fail and return a ForensicOutput
//        assertNotNull(result);
//        assertNotNull(result.getCommandRequest());
//        assertNotNull(result.getForensicItemList());
//    }
//
//    @Test
//    public void testCommandRequestCreation() throws Exception {
//        // Initialize the extractor first
//        riverbedForensicsExtractor.initialize();
//
//        // Test extract to verify command request creation
//        ForensicOutput result = riverbedForensicsExtractor.extract(1000L, 2000L, new Object());
//
//        // Verify command request structure
//        CommandRequestProtos.CommandRequest commandRequest = result.getCommandRequest();
//        assertNotNull(commandRequest);
//        assertEquals("test-supervisor", commandRequest.getSupervisorIdentifiers(0));
//        assertEquals("test-agent", commandRequest.getAgentIdentifier());
//        assertEquals("RIVERBED_FORENSICS", commandRequest.getAgentType());
//        assertEquals("SCHEDULED", commandRequest.getTriggerSource());
//        assertFalse(commandRequest.getCommandsList().isEmpty());
//
//        CommandRequestProtos.Command command = commandRequest.getCommands(0);
//        assertEquals("FORENSICS_COLLECTION", command.getCommandType());
//        assertEquals("collect_forensics_data", command.getCommand());
//        assertNotNull(command.getCommandJobId());
//    }
//
//    @Test
//    public void testForensicsTypeProcessing() throws Exception {
//        // Initialize the extractor first
//        riverbedForensicsExtractor.initialize();
//
//        // Set multiple forensics types
//        parameters.put("forensics.types", "network_traffic,security_events,application_flows");
//        riverbedForensicsExtractor.parameters = parameters;
//
//        // Mock XML parser to return different data for each type
//        when(xmlParser.buildForensicsRecords(anyString(), eq("network_traffic")))
//                .thenReturn(createMockForensicsRecord("network_session"));
//        when(xmlParser.buildForensicsRecords(anyString(), eq("security_events")))
//                .thenReturn(createMockForensicsRecord("security_session"));
//        when(xmlParser.buildForensicsRecords(anyString(), eq("application_flows")))
//                .thenReturn(createMockForensicsRecord("app_session"));
//
//        // Mock HTTP connection
//        riverbedForensicsExtractor.httpConnection = mock(com.heal.etladapter.connections.HttpConnection.class);
//        when(riverbedForensicsExtractor.httpConnection.httpPost(anyString(), anyString(), anyMap(), anyString()))
//                .thenReturn("<xml>test response</xml>");
//
//        // Test extract
//        ForensicOutput result = riverbedForensicsExtractor.extract(1000L, 2000L, ForensicOutput.builder().build());
//
//        // Should return ForensicOutput with items from all types
//        assertNotNull(result);
//        assertEquals(3, result.getForensicItemList().size());
//
//        // Verify different session IDs
//        Set<String> sessionIds = new HashSet<>();
//        for (ForensicItem item : result.getForensicItemList()) {
//            sessionIds.add(item.getItemId());
//        }
//        assertEquals(3, sessionIds.size());
//    }
//
//    private Map<String, Map<String, Object>> createMockForensicsRecord(String sessionId) {
//        Map<String, Map<String, Object>> record = new HashMap<>();
//        Map<String, Object> data = new HashMap<>();
//        data.put("source_ip", "***********");
//        data.put("destination_ip", "********");
//        data.put("protocol", "TCP");
//        record.put(sessionId, data);
//        return record;
//    }
//}
