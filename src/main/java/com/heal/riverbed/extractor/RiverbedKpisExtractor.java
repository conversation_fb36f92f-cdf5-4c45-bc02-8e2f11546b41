package com.heal.riverbed.extractor;

import com.google.common.net.HttpHeaders;
import com.heal.etladapter.beans.DomainEntityTypeKpi;
import com.heal.etladapter.extractors.HttpExtractor;
import com.heal.etladapter.pojos.DomainKpi;
import com.heal.etladapter.repo.mysql.ConnectorKpiMaster;
import com.heal.etladapter.utility.AdapterConstants;
import com.heal.riverbed.constants.Constants;
import com.heal.riverbed.exceptions.RiverbedConnectorException;
import com.heal.riverbed.parser.XmlParser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class RiverbedKpisExtractor extends HttpExtractor<Object, List<DomainKpi>> {

    @Autowired
    protected ConnectorKpiMaster connectorKpiMaster;
    protected Map<String, List<DomainEntityTypeKpi>> riverbedIntfKpisMap;

    @Autowired
    protected XmlParser xmlParser;

    @Override
    public void initialize() throws Exception {
        super.initialize();

        String riverbedUrl = this.parameters.get(Constants.RIVERBED_URL);
        if (riverbedUrl == null || riverbedUrl.trim().isEmpty()) {
            log.error("Riverbed url details unavailable in worker parameters for jobId:{}, connector instance:{}. Failing initialization for {}", jobId, connectorInstanceIdentifier, this.className);
            healthMetrics.putInExtractorErrors(this.connectorInstanceIdentifier.concat(":").concat(Constants.RIVERBED_KPI_EXTRACTOR_CONFIGURATION_ERROR), 1);
            throw new RiverbedConnectorException("Riverbed url details unavailable in worker parameters for jobId:" + jobId + ", connector instance:" + connectorInstanceIdentifier);
        }

        String riverbedKpiRequest = this.parameters.get(Constants.RIVERBED_KPI_REQUEST);
        if (riverbedKpiRequest == null || riverbedKpiRequest.trim().isEmpty()) {
            log.error("Riverbed KPI request is unavailable in worker parameters for jobId:{}, connector instance:{}. Failing initialization for {}", jobId, connectorInstanceIdentifier, this.className);
            healthMetrics.putInExtractorErrors(this.connectorInstanceIdentifier.concat(":").concat(Constants.RIVERBED_KPI_EXTRACTOR_CONFIGURATION_ERROR), 1);
            throw new RiverbedConnectorException("Riverbed KPI request details unavailable in worker parameters for jobId:" + jobId + ", connector instance:" + connectorInstanceIdentifier);
        }
        String token = this.parameters.get(Constants.RIVERBED_AUTHORIZATION);
        if (token == null || token.trim().isEmpty()) {
            log.error("Riverbed authorization token is unavailable in worker parameters for jobId:{}, connector instance:{}. Failing initialization for {}", jobId, connectorInstanceIdentifier, this.className);
            healthMetrics.putInExtractorErrors(this.connectorInstanceIdentifier.concat(":").concat(Constants.RIVERBED_KPI_EXTRACTOR_CONFIGURATION_ERROR), 1);
            throw new RiverbedConnectorException("Riverbed Authorization details unavailable in worker parameters for jobId:" + jobId + ", connector instance:" + connectorInstanceIdentifier);
        }

        List<DomainEntityTypeKpi> healKPIsForEntity = connectorKpiMaster.getHealKPIsForEntity(this.parameters.get(AdapterConstants.DOMAIN), this.jdbcTemplate);
        if (healKPIsForEntity == null || healKPIsForEntity.isEmpty()) {
            log.error("Riverbed kpis unavailable in Heal for jobId:{}, connector instance:{}. Failing initialization for {}", jobId, connectorInstanceIdentifier, this.className);
            healthMetrics.putInExtractorErrors(this.connectorInstanceIdentifier.concat(":").concat(Constants.RIVERBED_KPI_EXTRACTOR_CONFIGURATION_ERROR), 1);
            throw new RiverbedConnectorException("Riverbed kpis unavailable in Heal for jobId:" + jobId + ", connector instance:" + connectorInstanceIdentifier);
        }

        String entityTypeNames = this.parameters.get(Constants.RIVERBED_ENTITY_TYPES);
        if (entityTypeNames != null && !entityTypeNames.trim().isEmpty()) {
            List<String> entityTypes = Arrays.stream(entityTypeNames.split(","))
                    .map(String::trim)
                    .map(String::toLowerCase)
                    .toList();
            healKPIsForEntity = healKPIsForEntity.stream()
                    .filter(entity -> entityTypes.contains(entity.getEntityType().toLowerCase()))
                    .toList();
            log.info("Filtered KPIs for the specified entity types: {}, kpi size: {}, jobId:{}, connector instance:{}", entityTypes, healKPIsForEntity.size(), jobId, connectorInstanceIdentifier);
        }

        riverbedIntfKpisMap = healKPIsForEntity.stream().collect(Collectors.groupingBy(DomainEntityTypeKpi::getEntityIdentifier));

        if (riverbedIntfKpisMap.isEmpty()) {
            log.error("Riverbed kpis unavailable for the entity types: {} in Heal for jobId:{}, connector instance:{}. Failing initialization for {}", entityTypeNames, jobId, connectorInstanceIdentifier, this.className);
            healthMetrics.putInExtractorErrors(this.connectorInstanceIdentifier.concat(":").concat(Constants.RIVERBED_KPI_EXTRACTOR_CONFIGURATION_ERROR), 1);
            throw new RiverbedConnectorException("Riverbed kpis unavailable for the entity types in Heal for jobId:" + jobId + ", connector instance:" + connectorInstanceIdentifier);
        }

        log.trace("Configured kpi details: {} for jobId:{}, connector instance:{}", riverbedIntfKpisMap, jobId, connectorInstanceIdentifier);
    }

    @Override
    public List<DomainKpi> extract(long from, long to, Object items) {
        long st = System.currentTimeMillis();
        log.trace("Extracting kpis data from: {} to: {}, jobId:{}, connector instance:{}", from, to, jobId, connectorInstanceIdentifier);
        List<DomainKpi> kpiList = new ArrayList<>();

        try {

            String intfTypes = this.parameters.getOrDefault("types", "apt#:1,apt#:2,hos#:1,hos#:2");
            String template = this.parameters.get(Constants.RIVERBED_KPI_REQUEST).trim();
            riverbedIntfKpisMap.forEach((scrInstIdentifier, kpis) ->
                    kpiList.addAll(Arrays.stream(intfTypes.split(","))
                            .map(String::trim)
                            .map(s -> s.split("#"))
                            .filter(f -> f.length == 2)
                            .map(intfType -> {
                                try {
                                    String groupBy = intfType[0];
                                    String intfName = intfType[1];
                                    String prefix = groupBy + "_" + intfName;
                                    String limit = this.parameters.getOrDefault(prefix + "_limit", "10");
                                    String resolution = this.parameters.getOrDefault(prefix + "_resolution", "1min");
                                    String columns = this.parameters.getOrDefault(prefix + "_columns", "[6,57,33,34]");

                                    String payload = xmlParser.prepareRequestPayload(scrInstIdentifier, columns, intfName, groupBy, limit, resolution, from, to, template);

                                    Map<Integer, String> kpiColumns = xmlParser.getKpiColumnIndex(scrInstIdentifier, this.parameters.getOrDefault(prefix + "_kpis", ""));
                                    int attrIndex = Integer.parseInt(this.parameters.getOrDefault(prefix + "_attrIndex", "0"));
                                    return extractAndProcessKpi(scrInstIdentifier, from, to, payload, kpiColumns, attrIndex, kpis);
                                } catch (Exception e) {
                                    log.error("Error occurred while processing the entity:{}, jobId:{}, connector instance:{}", scrInstIdentifier, jobId, connectorInstanceIdentifier, e);
                                    return null;
                                }
                            }).filter(Objects::nonNull).flatMap(List::stream)
                            .toList()));
        } catch (Exception e) {
            healthMetrics.putInExtractorErrors(this.connectorInstanceIdentifier.concat(":").concat(Constants.RIVERBED_KPI_EXTRACTOR_ERRORS), 1);
            log.error("Error occurred in Extractor for jobId:{}, connector instance:{}", jobId, connectorInstanceIdentifier, e);
        }

        log.trace("Kpis fetched for jobId:{}, connector instance:{}, data: {}", jobId, connectorInstanceIdentifier, kpiList);
        log.info("Time taken to complete riverbed kpis extractor: {}ms, kpi size: {}, jobId:{}, connector instance:{}", (System.currentTimeMillis() - st), kpiList.size(), jobId, connectorInstanceIdentifier);
        return kpiList;
    }

    private List<DomainKpi> extractAndProcessKpi(String scrInstIdentifier, long from, long to, String payload, Map<Integer, String> columnMap, Integer columnAttribute, List<DomainEntityTypeKpi> metrics) {
        try {
            Map<String, DomainEntityTypeKpi> monitorKpiMap = metrics.stream().collect(Collectors.toMap(DomainEntityTypeKpi::getSrcMetricIdentifier, Function.identity(), (a, b) -> a));
            Map<String, String> headers = Map.of(
                    HttpHeaders.CONTENT_TYPE, "application/json",
                    HttpHeaders.AUTHORIZATION, this.parameters.get(Constants.RIVERBED_AUTHORIZATION)
            );
            String riverbedUrl = this.parameters.get(Constants.RIVERBED_URL);
            log.info("Making riverbed api call Url: {}, jobId:{}, connector instance:{}", riverbedUrl, jobId, connectorInstanceIdentifier);
            log.trace("Adding headers for riverbed api call:{}, headers {}, jobId:{}, connector instance:{}", riverbedUrl, headers, jobId, connectorInstanceIdentifier);
            String response = httpConnection.httpPost(riverbedUrl, payload, headers, this.connectorInstanceIdentifier);
            if (response == null || response.isEmpty()) {
                log.error("Invalid response received from Riverbed for url : {}, request : {}, response :{}, jobId:{}, connector instance:{}", riverbedUrl, payload, response, jobId, connectorInstanceIdentifier);
                return Collections.emptyList();
            }

            // Convert the api response to Map with Key is attribute, value map has kpi name and kpi value.
            Map<String, Map<String, Double>> attributeKpiNameData = xmlParser.buildKpiRecords(response, columnAttribute, columnMap, scrInstIdentifier);

            // Convert the map to another map key is kpi name, value map has attribute name and value.
            Map<String, Map<String, Double>> kpiNameAttributesData = xmlParser.pivotMap(attributeKpiNameData, scrInstIdentifier);
            return processResponse(scrInstIdentifier, kpiNameAttributesData, from, to, monitorKpiMap);
        } catch (Exception e) {
            log.error("Failed processing Kpi for entity: {}, jobId:{}, connector instance:{}", scrInstIdentifier, jobId, connectorInstanceIdentifier, e);
            return Collections.emptyList();
        }
    }


    private List<DomainKpi> processResponse(String sourceInstanceId, Map<String, Map<String, Double>> kpiAttributeData, long from, long to, Map<String, DomainEntityTypeKpi> monitorKpiMap) {
        List<DomainKpi> domainKpis = new ArrayList<>();
        Map<String, Map<String, Double>> kpiData = new HashMap<>();
        Date fromDate = new Date(from);
        Date toDate = new Date(to);

        kpiAttributeData.forEach((kpiName, attributeMap) -> {
            DomainEntityTypeKpi kpiDetail = monitorKpiMap.get(kpiName);
            if (kpiDetail == null) {
                log.error("Kpi details not present in heal for jobId:{}, connector instance:{}, data: {}", jobId, connectorInstanceIdentifier, kpiData);
                return;
            }
            if (!kpiDetail.isGroupKpi()) {
                domainKpis.add(populateKpiData(sourceInstanceId, attributeMap.get("ALL"), fromDate, toDate, kpiDetail));
            } else if (kpiDetail.isGroupKpi()) {
                domainKpis.add(populateGroupKpiData(sourceInstanceId, fromDate, toDate, attributeMap, kpiDetail));
            } else {
                log.error("Kpi details are not configured properly, KpiData:{}, from:{}, to:{}, jobId:{}, connector instance:{}", kpiData, fromDate, toDate, jobId, connectorInstanceIdentifier);
            }
        });
        log.debug("Number of kpis populated for jobId:{}, connector instance:{}, size:{}", jobId, connectorInstanceIdentifier, domainKpis.size());
        healthMetrics.updateKpisProcessedCount(domainKpis.size());
        return domainKpis;
    }

    private DomainKpi populateKpiData(String srcInstIdentifier, Double kpiValue, Date from, Date to, DomainEntityTypeKpi kpiDetail) {
        try {
            DomainKpi kpi = new DomainKpi();
            kpi.setLowerThreshold(from);
            kpi.setUpperThreshold(to);
            kpi.setKpiName(kpiDetail.getSrcMetricIdentifier());
            kpi.setDomainInstanceId(srcInstIdentifier);
            kpi.setValue(kpiValue);
            kpi.setIsGroupKpi(false);
            return kpi;
        } catch (Exception e) {
            log.error("Exception occurred while extracting the data for the instance: {}, Kpi: {}, for the time: {} to {}, jobId:{}, connector instance:{} ", srcInstIdentifier, kpiDetail, from, to, jobId, connectorInstanceIdentifier, e);
        }
        return null;
    }

    private DomainKpi populateGroupKpiData(String srcInstIdentifier, Date from, Date to, Map<String, Double> groupKpiAttributes, DomainEntityTypeKpi kpiDetail) {
        try {
            DomainKpi kpi = new DomainKpi();
            kpi.setLowerThreshold(from);
            kpi.setUpperThreshold(to);
            kpi.setKpiUid(kpiDetail.getMstKpiId());
            kpi.setKpiName(kpiDetail.getSrcMetricIdentifier());
            kpi.setDomainInstanceId(srcInstIdentifier);
            kpi.setIsGroupKpi(kpiDetail.isGroupKpi());
            kpi.setKpiGroupName(kpiDetail.getMstGroupName());
            kpi.setGroupName(kpiDetail.getMstGroupName());
            kpi.setGroupKpis(groupKpiAttributes.entrySet().stream()
                    .collect(Collectors
                            .toMap(Map.Entry::getKey,
                                    entry -> String.valueOf(entry.getValue()))));
            return kpi;
        } catch (Exception e) {
            log.error("Exception occurred while extracting the data for the instance: {}, group Kpi: {}, for the time: {} to {}, jobId:{}, connector instance:{}", srcInstIdentifier, kpiDetail, from, to, jobId, connectorInstanceIdentifier, e);
        }
        return null;
    }
}