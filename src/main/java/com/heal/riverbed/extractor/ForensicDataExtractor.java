package com.heal.riverbed.extractor;

import com.appnomic.appsone.common.protbuf.CommandRequestProtos;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.etladapter.aop.LogExecutionAnnotation;
import com.heal.etladapter.beans.AdapterHealthMetrics;
import com.heal.etladapter.extractors.AbstractExtractor;
import com.heal.etladapter.pojos.DomainKpi;
import com.heal.etladapter.pojos.ForensicItem;
import com.heal.etladapter.pojos.ForensicOutput;
import com.heal.etladapter.pojos.KpiType;
import com.heal.etladapter.utility.AdapterConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.stereotype.Component;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;

/**
 * ForensicDataExtractor is responsible for extracting forensic data from various sources
 * and creating ForensicOutput objects containing CommandRequest and list of ForensicItems.
 *
 * This extractor supports multiple data sources:
 * - Database queries for KPI and performance data
 * - Command request generation for forensic collection
 * - DomainKpi data extraction with configurable filters
 *
 * Key Features:
 * - Configurable SQL queries for data extraction
 * - Command request generation with customizable parameters
 * - Support for time-based data filtering
 * - Health metrics integration for monitoring
 * - Comprehensive error handling and logging
 *
 * Configuration Parameters:
 * - forensic.sql.query: SQL query to extract forensic data
 * - forensic.command.type: Type of forensic command (default: FORENSIC_COLLECTION)
 * - forensic.agent.identifier: Agent identifier for command requests
 * - forensic.agent.type: Agent type (default: FORENSIC_AGENT)
 * - forensic.supervisor.identifier: Supervisor identifier
 * - forensic.trigger.source: Trigger source (default: SCHEDULED)
 * - forensic.batch.size: Batch size for data processing (default: 100)
 * - forensic.kpi.filter.services: Comma-separated list of services to filter
 * - forensic.kpi.filter.threshold.min: Minimum threshold value filter
 * - forensic.kpi.filter.threshold.max: Maximum threshold value filter
 */
@Slf4j
@Component
public class ForensicDataExtractor extends AbstractExtractor<Object, ForensicOutput> {

    @Autowired
    private ObjectMapper objectMapper;
    
    @Autowired
    private AdapterHealthMetrics healthMetrics;

    // Configuration parameter keys
    private static final String FORENSIC_SQL_QUERY = "forensic.sql.query";
    private static final String FORENSIC_COMMAND_TYPE = "forensic.command.type";
    private static final String FORENSIC_AGENT_IDENTIFIER = "forensic.agent.identifier";
    private static final String FORENSIC_AGENT_TYPE = "forensic.agent.type";
    private static final String FORENSIC_SUPERVISOR_IDENTIFIER = "forensic.supervisor.identifier";
    private static final String FORENSIC_TRIGGER_SOURCE = "forensic.trigger.source";
    private static final String FORENSIC_BATCH_SIZE = "forensic.batch.size";
    private static final String FORENSIC_KPI_FILTER_SERVICES = "forensic.kpi.filter.services";
    private static final String FORENSIC_KPI_FILTER_THRESHOLD_MIN = "forensic.kpi.filter.threshold.min";
    private static final String FORENSIC_KPI_FILTER_THRESHOLD_MAX = "forensic.kpi.filter.threshold.max";

    // Default values
    private static final String DEFAULT_COMMAND_TYPE = "FORENSIC_COLLECTION";
    private static final String DEFAULT_AGENT_TYPE = "FORENSIC_AGENT";
    private static final String DEFAULT_TRIGGER_SOURCE = "SCHEDULED";
    private static final int DEFAULT_BATCH_SIZE = 100;

    private String sqlQuery;
    private String commandType;
    private String agentIdentifier;
    private String agentType;
    private String supervisorIdentifier;
    private String triggerSource;
    private int batchSize;
    private Set<String> serviceFilters;
    private Double minThreshold;
    private Double maxThreshold;

    /**
     * Initializes the ForensicDataExtractor with configuration parameters.
     * 
     * Validates required parameters and sets up filters and database connections.
     * 
     * @throws Exception if initialization fails or required parameters are missing
     */
    @Override
    @LogExecutionAnnotation
    public void initialize() throws Exception {
        log.info("Initialization for extractor {} - BEGIN", this.className);

        try {
            // Validate required parameters
            validateRequiredParameters();
            
            // Initialize configuration parameters
            initializeParameters();
            
            // Validate database connection
            validateDatabaseConnection();
            
            log.info("Forensic data extractor initialized successfully for jobId:{}, connector instance:{}", 
                    jobId, connectorInstanceIdentifier);
                    
        } catch (Exception e) {
            log.error("Error in initializing extractor {} for jobId:{}, connector instance:{}", 
                    this.className, jobId, connectorInstanceIdentifier, e);
            healthMetrics.putInExtractorErrors(AdapterConstants.FORENSIC_EXTRACTOR_INITIALIZATION_ERROR, 1);
            throw new Exception("Failed to initialize forensic data extractor: " + e.getMessage());
        }

        log.info("Initialization done for extractor {} for jobId:{}, connector instance:{}", 
                this.className, jobId, connectorInstanceIdentifier);
    }

    /**
     * Extracts forensic data for the specified time range.
     * 
     * Creates ForensicOutput containing:
     * - CommandRequest with forensic collection commands
     * - List of ForensicItems (DomainKpi objects) extracted from database
     * 
     * @param from Start time for data extraction (epoch milliseconds)
     * @param to End time for data extraction (epoch milliseconds)
     * @param items Input items (not used in this implementation)
     * @return ForensicOutput containing command request and forensic items
     */
    @Override
    @LogExecutionAnnotation
    public ForensicOutput extract(long from, long to, Object items) {
        try {
            log.info("Starting forensic data extraction for time range {} to {} for jobId:{}, connector instance:{}", 
                    new Date(from), new Date(to), jobId, connectorInstanceIdentifier);

            // Extract forensic items from database
            List<ForensicItem> forensicItems = extractForensicItems(from, to);
            
            // Generate command request for forensic collection
            CommandRequestProtos.CommandRequest commandRequest = generateCommandRequest(from, to, forensicItems.size());
            
            // Create and return ForensicOutput
            ForensicOutput forensicOutput = ForensicOutput.builder()
                    .commandRequest(commandRequest)
                    .forensicItemList(forensicItems)
                    .build();

            log.info("Successfully extracted {} forensic items for jobId:{}, connector instance:{}", 
                    forensicItems.size(), jobId, connectorInstanceIdentifier);
            
            healthMetrics.putInExtractorReceivedCount(this.className, forensicItems.size());
            
            return forensicOutput;
            
        } catch (Exception e) {
            log.error("Error extracting forensic data for jobId:{}, connector instance:{}", 
                    jobId, connectorInstanceIdentifier, e);
            healthMetrics.putInExtractorErrors(AdapterConstants.FORENSIC_EXTRACTOR_ERROR, 1);
            return ForensicOutput.builder()
                    .commandRequest(generateEmptyCommandRequest())
                    .forensicItemList(new ArrayList<>())
                    .build();
        }
    }

    /**
     * Validates required configuration parameters.
     */
    private void validateRequiredParameters() throws Exception {
        if (parameters == null || parameters.isEmpty()) {
            throw new Exception("No parameters provided for forensic data extractor");
        }

        if (!parameters.containsKey(FORENSIC_SQL_QUERY)) {
            throw new Exception("Required parameter missing: " + FORENSIC_SQL_QUERY);
        }

        if (!parameters.containsKey(FORENSIC_AGENT_IDENTIFIER)) {
            throw new Exception("Required parameter missing: " + FORENSIC_AGENT_IDENTIFIER);
        }

        if (!parameters.containsKey(FORENSIC_SUPERVISOR_IDENTIFIER)) {
            throw new Exception("Required parameter missing: " + FORENSIC_SUPERVISOR_IDENTIFIER);
        }
    }

    /**
     * Initializes configuration parameters from the parameters map.
     */
    private void initializeParameters() {
        sqlQuery = parameters.get(FORENSIC_SQL_QUERY);
        commandType = parameters.getOrDefault(FORENSIC_COMMAND_TYPE, DEFAULT_COMMAND_TYPE);
        agentIdentifier = parameters.get(FORENSIC_AGENT_IDENTIFIER);
        agentType = parameters.getOrDefault(FORENSIC_AGENT_TYPE, DEFAULT_AGENT_TYPE);
        supervisorIdentifier = parameters.get(FORENSIC_SUPERVISOR_IDENTIFIER);
        triggerSource = parameters.getOrDefault(FORENSIC_TRIGGER_SOURCE, DEFAULT_TRIGGER_SOURCE);
        batchSize = Integer.parseInt(parameters.getOrDefault(FORENSIC_BATCH_SIZE, String.valueOf(DEFAULT_BATCH_SIZE)));

        // Initialize filters
        String servicesFilter = parameters.get(FORENSIC_KPI_FILTER_SERVICES);
        if (servicesFilter != null && !servicesFilter.trim().isEmpty()) {
            serviceFilters = new HashSet<>(Arrays.asList(servicesFilter.split(",")));
        }

        String minThresholdStr = parameters.get(FORENSIC_KPI_FILTER_THRESHOLD_MIN);
        if (minThresholdStr != null && !minThresholdStr.trim().isEmpty()) {
            minThreshold = Double.parseDouble(minThresholdStr);
        }

        String maxThresholdStr = parameters.get(FORENSIC_KPI_FILTER_THRESHOLD_MAX);
        if (maxThresholdStr != null && !maxThresholdStr.trim().isEmpty()) {
            maxThreshold = Double.parseDouble(maxThresholdStr);
        }

        log.info("Initialized parameters - Command Type: {}, Agent: {}, Supervisor: {}, Batch Size: {}", 
                commandType, agentIdentifier, supervisorIdentifier, batchSize);
    }

    /**
     * Validates database connection availability.
     */
    private void validateDatabaseConnection() throws Exception {
        if (jdbcTemplate == null) {
            throw new Exception("JdbcTemplate is not available for database operations");
        }
        
        try {
            jdbcTemplate.queryForObject("SELECT 1", Integer.class);
        } catch (Exception e) {
            throw new Exception("Database connection validation failed: " + e.getMessage());
        }
    }

    /**
     * Extracts forensic items from the database using the configured SQL query.
     */
    private List<ForensicItem> extractForensicItems(long from, long to) {
        try {
            Date fromDate = new Date(from);
            Date toDate = new Date(to);
            
            log.debug("Executing SQL query: {} with parameters from={}, to={}", sqlQuery, fromDate, toDate);
            
            List<DomainKpi> domainKpis = jdbcTemplate.query(
                    sqlQuery,
                    new Object[]{fromDate, toDate},
                    this::mapRowToDomainKpi
            );
            
            // Apply filters if configured
            List<ForensicItem> filteredItems = applyFilters(domainKpis);
            
            log.info("Extracted {} forensic items (filtered from {} total) for time range {} to {}", 
                    filteredItems.size(), domainKpis.size(), fromDate, toDate);
            
            return filteredItems;
            
        } catch (EmptyResultDataAccessException e) {
            log.info("No forensic data found for time range {} to {}", new Date(from), new Date(to));
            return new ArrayList<>();
        } catch (Exception e) {
            log.error("Error executing forensic data query", e);
            throw new RuntimeException("Failed to extract forensic items: " + e.getMessage());
        }
    }

    /**
     * Maps database result set row to DomainKpi object.
     */
    private DomainKpi mapRowToDomainKpi(ResultSet rs, int rowNum) throws SQLException {
        return DomainKpi.builder()
                .kpiName(rs.getString("kpi_name"))
                .value(rs.getDouble("kpi_value"))
                .serviceName(rs.getString("service_name"))
                .domainInstanceId(rs.getString("domain_instance_id"))
                .collectionInterval(rs.getInt("collection_interval"))
                .lowerThreshold(rs.getTimestamp("lower_threshold"))
                .upperThreshold(rs.getTimestamp("upper_threshold"))
                .kpiType(KpiType.valueOf(rs.getString("kpi_type")))
                .healInstance(rs.getString("heal_instance"))
                .entryMetric(rs.getDouble("entry_metric"))
                .exitMetric(rs.getDouble("exit_metric"))
                .build();
    }

    /**
     * Applies configured filters to the extracted domain KPIs.
     */
    private List<ForensicItem> applyFilters(List<DomainKpi> domainKpis) {
        return domainKpis.stream()
                .filter(this::passesServiceFilter)
                .filter(this::passesThresholdFilter)
                .map(kpi -> (ForensicItem) kpi)
                .toList();
    }

    /**
     * Checks if the KPI passes the service filter.
     */
    private boolean passesServiceFilter(DomainKpi kpi) {
        if (serviceFilters == null || serviceFilters.isEmpty()) {
            return true;
        }
        return serviceFilters.contains(kpi.getServiceName());
    }

    /**
     * Checks if the KPI passes the threshold filter.
     */
    private boolean passesThresholdFilter(DomainKpi kpi) {
        if (kpi.getValue() == null) {
            return false;
        }
        
        if (minThreshold != null && kpi.getValue() < minThreshold) {
            return false;
        }
        
        if (maxThreshold != null && kpi.getValue() > maxThreshold) {
            return false;
        }
        
        return true;
    }

    /**
     * Generates CommandRequest for forensic data collection.
     */
    private CommandRequestProtos.CommandRequest generateCommandRequest(long from, long to, int itemCount) {
        String commandJobId = "forensic-job-" + System.currentTimeMillis();
        String command = String.format("collect-forensics --from=%d --to=%d --count=%d", from, to, itemCount);
        
        CommandRequestProtos.Command forensicCommand = CommandRequestProtos.Command.newBuilder()
                .setCommandJobId(commandJobId)
                .setCommandType(commandType)
                .setCommand(command)
                .build();

        return CommandRequestProtos.CommandRequest.newBuilder()
                .setAgentIdentifier(agentIdentifier)
                .setAgentType(agentType)
                .setTriggerSource(triggerSource)
                .setTriggerTime(System.currentTimeMillis())
                .addSupervisorIdentifiers(supervisorIdentifier)
                .addCommands(forensicCommand)
                .build();
    }

    /**
     * Generates empty CommandRequest for error scenarios.
     */
    private CommandRequestProtos.CommandRequest generateEmptyCommandRequest() {
        CommandRequestProtos.Command emptyCommand = CommandRequestProtos.Command.newBuilder()
                .setCommandJobId("empty-job-" + System.currentTimeMillis())
                .setCommandType("EMPTY")
                .setCommand("no-operation")
                .build();

        return CommandRequestProtos.CommandRequest.newBuilder()
                .setAgentIdentifier(agentIdentifier != null ? agentIdentifier : "unknown-agent")
                .setAgentType(agentType != null ? agentType : "UNKNOWN")
                .setTriggerSource("ERROR")
                .setTriggerTime(System.currentTimeMillis())
                .addSupervisorIdentifiers(supervisorIdentifier != null ? supervisorIdentifier : "unknown-supervisor")
                .addCommands(emptyCommand)
                .build();
    }
}
