package com.heal.riverbed.extractor;

import com.appnomic.appsone.common.protbuf.CommandRequestProtos;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.net.HttpHeaders;
import com.heal.configuration.enums.KpiType;
import com.heal.etladapter.aop.LogExecutionAnnotation;
import com.heal.etladapter.beans.AdapterHealthMetrics;
import com.heal.etladapter.beans.DomainEntityTypeKpi;
import com.heal.etladapter.extractors.HttpExtractor;
import com.heal.etladapter.pojos.DomainKpi;
import com.heal.etladapter.pojos.ForensicItem;
import com.heal.etladapter.pojos.ForensicOutput;
import com.heal.etladapter.repo.mysql.ConnectorKpiMaster;
import com.heal.etladapter.utility.AdapterConstants;
import com.heal.riverbed.constants.Constants;
import com.heal.riverbed.exceptions.RiverbedConnectorException;
import com.heal.riverbed.parser.XmlParser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.stereotype.Component;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * ForensicDataExtractor is responsible for extracting forensic data from various sources
 * and creating ForensicOutput objects containing CommandRequest and list of ForensicItems.
 *
 * This extractor supports multiple data sources:
 * - HTTP API calls to Riverbed systems for KPI and performance data
 * - Command request generation for forensic collection
 * - DomainKpi data extraction with configurable filters
 *
 * Key Features:
 * - HTTP-based data extraction from Riverbed systems
 * - Command request generation with customizable parameters
 * - Support for time-based data filtering
 * - Health metrics integration for monitoring
 * - Comprehensive error handling and logging
 *
 * Configuration Parameters:
 * - riverbed.url: Riverbed system URL for API calls
 * - riverbed.kpi.request: KPI request template
 * - riverbed.authorization: Authorization token for API access
 * - forensic.command.type: Type of forensic command (default: FORENSIC_COLLECTION)
 * - forensic.agent.identifier: Agent identifier for command requests
 * - forensic.agent.type: Agent type (default: FORENSIC_AGENT)
 * - forensic.supervisor.identifier: Supervisor identifier
 * - forensic.trigger.source: Trigger source (default: SCHEDULED)
 * - forensic.batch.size: Batch size for data processing (default: 100)
 */
@Slf4j
@Component
public class ForensicDataExtractor extends HttpExtractor<Object, ForensicOutput> {

    @Autowired
    private ObjectMapper objectMapper;
    
    @Autowired
    protected ConnectorKpiMaster connectorKpiMaster;
    
    protected Map<String, List<DomainEntityTypeKpi>> riverbedIntfKpisMap;

    @Autowired
    protected XmlParser xmlParser;

    // Configuration parameter keys - keeping forensic-specific ones
    private static final String FORENSIC_COMMAND_TYPE = "forensic.command.type";
    private static final String FORENSIC_AGENT_IDENTIFIER = "forensic.agent.identifier";
    private static final String FORENSIC_AGENT_TYPE = "forensic.agent.type";
    private static final String FORENSIC_SUPERVISOR_IDENTIFIER = "forensic.supervisor.identifier";
    private static final String FORENSIC_TRIGGER_SOURCE = "forensic.trigger.source";
    private static final String FORENSIC_BATCH_SIZE = "forensic.batch.size";

    // Default values
    private static final String DEFAULT_COMMAND_TYPE = "FORENSIC_COLLECTION";
    private static final String DEFAULT_AGENT_TYPE = "FORENSIC_AGENT";
    private static final String DEFAULT_TRIGGER_SOURCE = "SCHEDULED";
    private static final int DEFAULT_BATCH_SIZE = 100;

    private String commandType;
    private String agentIdentifier;
    private String agentType;
    private String supervisorIdentifier;
    private String triggerSource;
    private int batchSize;

    @Override
    public void initialize() throws Exception {
        super.initialize();

        String riverbedUrl = this.parameters.get(Constants.RIVERBED_URL);
        if (riverbedUrl == null || riverbedUrl.trim().isEmpty()) {
            log.error("Riverbed url details unavailable in worker parameters for jobId:{}, connector instance:{}. Failing initialization for {}", jobId, connectorInstanceIdentifier, this.className);
            healthMetrics.putInExtractorErrors(this.connectorInstanceIdentifier.concat(":").concat(Constants.RIVERBED_KPI_EXTRACTOR_CONFIGURATION_ERROR), 1);
            throw new RiverbedConnectorException("Riverbed url details unavailable in worker parameters for jobId:" + jobId + ", connector instance:" + connectorInstanceIdentifier);
        }

        String riverbedKpiRequest = this.parameters.get(Constants.RIVERBED_KPI_REQUEST);
        if (riverbedKpiRequest == null || riverbedKpiRequest.trim().isEmpty()) {
            log.error("Riverbed KPI request is unavailable in worker parameters for jobId:{}, connector instance:{}. Failing initialization for {}", jobId, connectorInstanceIdentifier, this.className);
            healthMetrics.putInExtractorErrors(this.connectorInstanceIdentifier.concat(":").concat(Constants.RIVERBED_KPI_EXTRACTOR_CONFIGURATION_ERROR), 1);
            throw new RiverbedConnectorException("Riverbed KPI request details unavailable in worker parameters for jobId:" + jobId + ", connector instance:" + connectorInstanceIdentifier);
        }
        
        String token = this.parameters.get(Constants.RIVERBED_AUTHORIZATION);
        if (token == null || token.trim().isEmpty()) {
            log.error("Riverbed authorization token is unavailable in worker parameters for jobId:{}, connector instance:{}. Failing initialization for {}", jobId, connectorInstanceIdentifier, this.className);
            healthMetrics.putInExtractorErrors(this.connectorInstanceIdentifier.concat(":").concat(Constants.RIVERBED_KPI_EXTRACTOR_CONFIGURATION_ERROR), 1);
            throw new RiverbedConnectorException("Riverbed Authorization details unavailable in worker parameters for jobId:" + jobId + ", connector instance:" + connectorInstanceIdentifier);
        }

        // Initialize forensic-specific parameters
        initializeForensicParameters();

        List<DomainEntityTypeKpi> healKPIsForEntity = connectorKpiMaster.getHealKPIsForEntity(this.parameters.get(AdapterConstants.DOMAIN), this.jdbcTemplate);
        if (healKPIsForEntity == null || healKPIsForEntity.isEmpty()) {
            log.error("Riverbed kpis unavailable in Heal for jobId:{}, connector instance:{}. Failing initialization for {}", jobId, connectorInstanceIdentifier, this.className);
            healthMetrics.putInExtractorErrors(this.connectorInstanceIdentifier.concat(":").concat(Constants.RIVERBED_KPI_EXTRACTOR_CONFIGURATION_ERROR), 1);
            throw new RiverbedConnectorException("Riverbed kpis unavailable in Heal for jobId:" + jobId + ", connector instance:" + connectorInstanceIdentifier);
        }

        String entityTypeNames = this.parameters.get(Constants.RIVERBED_ENTITY_TYPES);
        if (entityTypeNames != null && !entityTypeNames.trim().isEmpty()) {
            List<String> entityTypes = Arrays.stream(entityTypeNames.split(","))
                    .map(String::trim)
                    .map(String::toLowerCase)
                    .toList();
            healKPIsForEntity = healKPIsForEntity.stream()
                    .filter(entity -> entityTypes.contains(entity.getEntityType().toLowerCase()))
                    .toList();
            log.info("Filtered KPIs for the specified entity types: {}, kpi size: {}, jobId:{}, connector instance:{}", entityTypes, healKPIsForEntity.size(), jobId, connectorInstanceIdentifier);
        }

        riverbedIntfKpisMap = healKPIsForEntity.stream().collect(Collectors.groupingBy(DomainEntityTypeKpi::getEntityIdentifier));

        if (riverbedIntfKpisMap.isEmpty()) {
            log.error("Riverbed kpis unavailable for the entity types: {} in Heal for jobId:{}, connector instance:{}. Failing initialization for {}", entityTypeNames, jobId, connectorInstanceIdentifier, this.className);
            healthMetrics.putInExtractorErrors(this.connectorInstanceIdentifier.concat(":").concat(Constants.RIVERBED_KPI_EXTRACTOR_CONFIGURATION_ERROR), 1);
            throw new RiverbedConnectorException("Riverbed kpis unavailable for the entity types in Heal for jobId:" + jobId + ", connector instance:" + connectorInstanceIdentifier);
        }

        log.trace("Configured kpi details: {} for jobId:{}, connector instance:{}", riverbedIntfKpisMap, jobId, connectorInstanceIdentifier);
    }

    @Override
    public ForensicOutput extract(long from, long to, Object items) {
        long st = System.currentTimeMillis();
        log.trace("Extracting forensic data from: {} to: {}, jobId:{}, connector instance:{}", from, to, jobId, connectorInstanceIdentifier);
        List<DomainKpi> kpiList = new ArrayList<>();

        try {
            String intfTypes = this.parameters.getOrDefault("types", "apt#:1,apt#:2,hos#:1,hos#:2");
            String template = this.parameters.get(Constants.RIVERBED_KPI_REQUEST).trim();
            riverbedIntfKpisMap.forEach((scrInstIdentifier, kpis) ->
                    kpiList.addAll(Arrays.stream(intfTypes.split(","))
                            .map(String::trim)
                            .map(s -> s.split("#"))
                            .filter(f -> f.length == 2)
                            .map(intfType -> {
                                try {
                                    String groupBy = intfType[0];
                                    String intfName = intfType[1];
                                    String prefix = groupBy + "_" + intfName;
                                    String limit = this.parameters.getOrDefault(prefix + "_limit", "10");
                                    String resolution = this.parameters.getOrDefault(prefix + "_resolution", "1min");
                                    String columns = this.parameters.getOrDefault(prefix + "_columns", "[6,57,33,34]");

                                    String payload = xmlParser.prepareRequestPayload(scrInstIdentifier, columns, intfName, groupBy, limit, resolution, from, to, template);

                                    Map<Integer, String> kpiColumns = xmlParser.getKpiColumnIndex(scrInstIdentifier, this.parameters.getOrDefault(prefix + "_kpis", ""));
                                    int attrIndex = Integer.parseInt(this.parameters.getOrDefault(prefix + "_attrIndex", "0"));
                                    return extractAndProcessKpi(scrInstIdentifier, from, to, payload, kpiColumns, attrIndex, kpis);
                                } catch (Exception e) {
                                    log.error("Error occurred while processing the entity:{}, jobId:{}, connector instance:{}", scrInstIdentifier, jobId, connectorInstanceIdentifier, e);
                                    return null;
                                }
                            }).filter(Objects::nonNull).flatMap(List::stream)
                            .toList()));
        } catch (Exception e) {
            healthMetrics.putInExtractorErrors(this.connectorInstanceIdentifier.concat(":").concat(Constants.RIVERBED_KPI_EXTRACTOR_ERRORS), 1);
            log.error("Error occurred in Extractor for jobId:{}, connector instance:{}", jobId, connectorInstanceIdentifier, e);
        }

        // Convert KPI list to ForensicOutput
        ForensicOutput forensicOutput = convertToForensicOutput(kpiList, from, to);
        
        log.trace("Forensic data fetched for jobId:{}, connector instance:{}, data: {}", jobId, connectorInstanceIdentifier, kpiList);
        log.info("Time taken to complete forensic data extractor: {}ms, kpi size: {}, jobId:{}, connector instance:{}", (System.currentTimeMillis() - st), kpiList.size(), jobId, connectorInstanceIdentifier);
        return forensicOutput;
    }

    /**
     * Initializes forensic-specific configuration parameters.
     */
    private void initializeForensicParameters() {
        commandType = parameters.getOrDefault(FORENSIC_COMMAND_TYPE, DEFAULT_COMMAND_TYPE);
        agentIdentifier = parameters.get(FORENSIC_AGENT_IDENTIFIER);
        agentType = parameters.getOrDefault(FORENSIC_AGENT_TYPE, DEFAULT_AGENT_TYPE);
        supervisorIdentifier = parameters.get(FORENSIC_SUPERVISOR_IDENTIFIER);
        triggerSource = parameters.getOrDefault(FORENSIC_TRIGGER_SOURCE, DEFAULT_TRIGGER_SOURCE);
        batchSize = Integer.parseInt(parameters.getOrDefault(FORENSIC_BATCH_SIZE, String.valueOf(DEFAULT_BATCH_SIZE)));

        log.info("Initialized forensic parameters - Command Type: {}, Agent: {}, Supervisor: {}, Batch Size: {}",
                commandType, agentIdentifier, supervisorIdentifier, batchSize);
    }

    private List<DomainKpi> extractAndProcessKpi(String scrInstIdentifier, long from, long to, String payload, Map<Integer, String> columnMap, Integer columnAttribute, List<DomainEntityTypeKpi> metrics) {
        try {
            Map<String, DomainEntityTypeKpi> monitorKpiMap = metrics.stream().collect(Collectors.toMap(DomainEntityTypeKpi::getSrcMetricIdentifier, Function.identity(), (a, b) -> a));
            Map<String, String> headers = Map.of(
                    HttpHeaders.CONTENT_TYPE, "application/json",
                    HttpHeaders.AUTHORIZATION, this.parameters.get(Constants.RIVERBED_AUTHORIZATION)
            );
            String riverbedUrl = this.parameters.get(Constants.RIVERBED_URL);
            log.info("Making riverbed api call Url: {}, jobId:{}, connector instance:{}", riverbedUrl, jobId, connectorInstanceIdentifier);
            log.trace("Adding headers for riverbed api call:{}, headers {}, jobId:{}, connector instance:{}", riverbedUrl, headers, jobId, connectorInstanceIdentifier);
            String response = httpConnection.httpPost(riverbedUrl, payload, headers, this.connectorInstanceIdentifier);
            if (response == null || response.isEmpty()) {
                log.error("Invalid response received from Riverbed for url : {}, request : {}, response :{}, jobId:{}, connector instance:{}", riverbedUrl, payload, response, jobId, connectorInstanceIdentifier);
                return Collections.emptyList();
            }

            // Convert the api response to Map with Key is attribute, value map has kpi name and kpi value.
            Map<String, Map<String, Double>> attributeKpiNameData = xmlParser.buildKpiRecords(response, columnAttribute, columnMap, scrInstIdentifier);

            // Convert the map to another map key is kpi name, value map has attribute name and value.
            Map<String, Map<String, Double>> kpiNameAttributesData = xmlParser.pivotMap(attributeKpiNameData, scrInstIdentifier);
            return processResponse(scrInstIdentifier, kpiNameAttributesData, from, to, monitorKpiMap);
        } catch (Exception e) {
            log.error("Failed processing Kpi for entity: {}, jobId:{}, connector instance:{}", scrInstIdentifier, jobId, connectorInstanceIdentifier, e);
            return Collections.emptyList();
        }
    }

    private List<DomainKpi> processResponse(String sourceInstanceId, Map<String, Map<String, Double>> kpiAttributeData, long from, long to, Map<String, DomainEntityTypeKpi> monitorKpiMap) {
        List<DomainKpi> domainKpis = new ArrayList<>();
        Map<String, Map<String, Double>> kpiData = new HashMap<>();
        Date fromDate = new Date(from);
        Date toDate = new Date(to);

        kpiAttributeData.forEach((kpiName, attributeMap) -> {
            DomainEntityTypeKpi kpiDetail = monitorKpiMap.get(kpiName);
            if (kpiDetail == null) {
                log.error("Kpi details not present in heal for jobId:{}, connector instance:{}, data: {}", jobId, connectorInstanceIdentifier, kpiData);
                return;
            }
            if (!kpiDetail.isGroupKpi()) {
                domainKpis.add(populateKpiData(sourceInstanceId, attributeMap.get("ALL"), fromDate, toDate, kpiDetail));
            } else if (kpiDetail.isGroupKpi()) {
                domainKpis.add(populateGroupKpiData(sourceInstanceId, fromDate, toDate, attributeMap, kpiDetail));
            } else {
                log.error("Kpi details are not configured properly, KpiData:{}, from:{}, to:{}, jobId:{}, connector instance:{}", kpiData, fromDate, toDate, jobId, connectorInstanceIdentifier);
            }
        });
        log.debug("Number of kpis populated for jobId:{}, connector instance:{}, size:{}", jobId, connectorInstanceIdentifier, domainKpis.size());
        healthMetrics.updateKpisProcessedCount(domainKpis.size());
        return domainKpis;
    }

    private DomainKpi populateKpiData(String srcInstIdentifier, Double kpiValue, Date from, Date to, DomainEntityTypeKpi kpiDetail) {
        try {
            DomainKpi kpi = new DomainKpi();
            kpi.setLowerThreshold(from);
            kpi.setUpperThreshold(to);
            kpi.setKpiName(kpiDetail.getSrcMetricIdentifier());
            kpi.setDomainInstanceId(srcInstIdentifier);
            kpi.setValue(kpiValue);
            kpi.setIsGroupKpi(false);
            return kpi;
        } catch (Exception e) {
            log.error("Exception occurred while extracting the data for the instance: {}, Kpi: {}, for the time: {} to {}, jobId:{}, connector instance:{} ", srcInstIdentifier, kpiDetail, from, to, jobId, connectorInstanceIdentifier, e);
        }
        return null;
    }

    private DomainKpi populateGroupKpiData(String srcInstIdentifier, Date from, Date to, Map<String, Double> groupKpiAttributes, DomainEntityTypeKpi kpiDetail) {
        try {
            DomainKpi kpi = new DomainKpi();
            kpi.setLowerThreshold(from);
            kpi.setUpperThreshold(to);
            kpi.setKpiUid(kpiDetail.getMstKpiId());
            kpi.setKpiName(kpiDetail.getSrcMetricIdentifier());
            kpi.setDomainInstanceId(srcInstIdentifier);
            kpi.setIsGroupKpi(kpiDetail.isGroupKpi());
            kpi.setKpiGroupName(kpiDetail.getMstGroupName());
            kpi.setGroupName(kpiDetail.getMstGroupName());
            kpi.setGroupKpis(groupKpiAttributes.entrySet().stream()
                    .collect(Collectors
                            .toMap(Map.Entry::getKey,
                                    entry -> String.valueOf(entry.getValue()))));
            return kpi;
        } catch (Exception e) {
            log.error("Exception occurred while extracting the data for the instance: {}, group Kpi: {}, for the time: {} to {}, jobId:{}, connector instance:{}", srcInstIdentifier, kpiDetail, from, to, jobId, connectorInstanceIdentifier, e);
        }
        return null;
    }

    /**
     * Converts the list of DomainKpi objects to ForensicOutput.
     */
    private ForensicOutput convertToForensicOutput(List<DomainKpi> kpiList, long from, long to) {
        try {
            // Convert DomainKpi objects to ForensicItems
            List<ForensicItem> forensicItems = kpiList.stream()
                    .map(kpi -> (ForensicItem) kpi)
                    .toList();

            // Generate command request for forensic collection
            CommandRequestProtos.CommandRequest commandRequest = generateCommandRequest(from, to, forensicItems.size());

            return ForensicOutput.builder()
                    .commandRequest(commandRequest)
                    .forensicItemList(forensicItems)
                    .build();

        } catch (Exception e) {
            log.error("Error converting KPI list to ForensicOutput for jobId:{}, connector instance:{}", jobId, connectorInstanceIdentifier, e);
            return ForensicOutput.builder()
                    .commandRequest(generateEmptyCommandRequest())
                    .forensicItemList(new ArrayList<>())
                    .build();
        }
    }

    /**
     * Generates CommandRequest for forensic data collection.
     */
    private CommandRequestProtos.CommandRequest generateCommandRequest(long from, long to, int itemCount) {
        String commandJobId = "forensic-job-" + System.currentTimeMillis();
        String command = String.format("collect-forensics --from=%d --to=%d --count=%d", from, to, itemCount);

        CommandRequestProtos.Command forensicCommand = CommandRequestProtos.Command.newBuilder()
                .setCommandJobId(commandJobId)
                .setCommandType(commandType != null ? commandType : DEFAULT_COMMAND_TYPE)
                .setCommand(command)
                .build();

        return CommandRequestProtos.CommandRequest.newBuilder()
                .setAgentIdentifier(agentIdentifier != null ? agentIdentifier : "unknown-agent")
                .setAgentType(agentType != null ? agentType : DEFAULT_AGENT_TYPE)
                .setTriggerSource(triggerSource != null ? triggerSource : DEFAULT_TRIGGER_SOURCE)
                .setTriggerTime(System.currentTimeMillis())
                .addSupervisorIdentifiers(supervisorIdentifier != null ? supervisorIdentifier : "unknown-supervisor")
                .addCommands(forensicCommand)
                .build();
    }

    /**
     * Generates empty CommandRequest for error scenarios.
     */
    private CommandRequestProtos.CommandRequest generateEmptyCommandRequest() {
        CommandRequestProtos.Command emptyCommand = CommandRequestProtos.Command.newBuilder()
                .setCommandJobId("empty-job-" + System.currentTimeMillis())
                .setCommandType("EMPTY")
                .setCommand("no-operation")
                .build();

        return CommandRequestProtos.CommandRequest.newBuilder()
                .setAgentIdentifier(agentIdentifier != null ? agentIdentifier : "unknown-agent")
                .setAgentType(agentType != null ? agentType : "UNKNOWN")
                .setTriggerSource("ERROR")
                .setTriggerTime(System.currentTimeMillis())
                .addSupervisorIdentifiers(supervisorIdentifier != null ? supervisorIdentifier : "unknown-supervisor")
                .addCommands(emptyCommand)
                .build();
    }
}
