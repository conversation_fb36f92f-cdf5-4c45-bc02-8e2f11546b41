package com.heal.riverbed.util;

import com.heal.configuration.protbuf.ScheduledJobProtos;
import com.rabbitmq.client.Channel;
import com.rabbitmq.client.Connection;
import com.rabbitmq.client.ConnectionFactory;

import java.util.ArrayList;
import java.util.List;

public class RMQKPIDataPush {
    public static void main(String[] argv) throws Exception {

        //String QUEUE_NAME = "DT-1_dynatrace_topology";
        //String QUEUE_NAME = "DT-1_dynatrace";
        String QUEUE_NAME = "riverbed-connector-1";
        //String QUEUE_NAME = "multi-connector-1";
        //String QUEUE_NAME = "NR-1_NewRelic";
        //String QUEUE_NAME = "K8s-1_connector";q
        //String QUEUE_NAME = "AppD-events";
        //String QUEUE_NAME = "Appd-1_Appdynamic";

        ConnectionFactory factory = new ConnectionFactory();
        factory.setHost("**************");
        //factory.setHost("localhost");
        factory.setPort(5672);
        factory.setUsername("guest");
        factory.setPassword("guest");
        //factory.useSslProtocol();

        //String binFilePath = "/home/<USER>/Downloads/scheduler_details_a.bin";
        //String binFilePath = "/home/<USER>/Downloads/appd-event.bin";

        int num = 2;

        try (Connection connection = factory.newConnection();
             Channel channel = connection.createChannel()) {

            // Declare a queue for us to send to; idempotent - it will only be created if it doesn't exist already.
            channel.queueDeclare(QUEUE_NAME, true, false, false, null);

            for (int i = 1; i <= num; i++) {
                //channel.basicPublish("", QUEUE_NAME, null, getAppdScheduler().toByteArray());
                //channel.basicPublish("", QUEUE_NAME, null, getDtscheduler().toByteArray());
                channel.basicPublish("", QUEUE_NAME, null, getRBscheduler().toByteArray());
                //channel.basicPublish("", QUEUE_NAME, null, getMultipleInstanceScheduler().toByteArray());
                //channel.basicPublish("", QUEUE_NAME, null, scheduledJob1.toByteArray());
                //channel.basicPublish("", QUEUE_NAME, null, getK8sScheduler().toByteArray());
            }
        }
    }

    private static ScheduledJobProtos.ScheduledJob getAppdScheduler() {
        ScheduledJobProtos.Arguments arguments1 = ScheduledJobProtos.Arguments.newBuilder()
                .setName("queue_name")
                .setValue("Appd-1_Appdynamic")
                .setDefaultValue("Appd-1_Appdynamic").build();
        ScheduledJobProtos.Arguments arguments2 = ScheduledJobProtos.Arguments.newBuilder()
                .setName("connector_instance")
                .setValue("APPD-1_KPI")
                .setDefaultValue("APPD-1_KPI").build();
        //String chain_id = "Appdynamic_Topology";
        String chain_id = "Appdynamic_KPIs";
        //String chain_id = "Appdynamic_Transactions";
        //String chain_id = "Appdynamic_Events";
        ScheduledJobProtos.Arguments arguments3 = ScheduledJobProtos.Arguments.newBuilder()
                .setName("connector_chain")
                .setValue(chain_id)
                .setDefaultValue(chain_id).build();
        List<ScheduledJobProtos.Arguments> argumentsList = new ArrayList<>();
        argumentsList.add(arguments1);
        argumentsList.add(arguments2);
        argumentsList.add(arguments3);
        ScheduledJobProtos.ScheduledJob scheduledJob = ScheduledJobProtos.ScheduledJob.newBuilder()
                .setAccountIdentifier("demo")
                .addAllSchedulerArguments(argumentsList)
                .setSchedulerType("Recurring")
                .setTriggerSource("Scheduler-Service")
                //.setJobImplementationType("DynatraceTopology")
                .setJobImplementationType("AppdynamicsKpi")
                //.setJobImplementationType("DynatraceTxn")
                //.setJobImplementationType("DynatraceEvents")
                .setJobType("JobTypes")
                .setScheduledJobId(374)
                .setTriggerTime(1716455057650L)
                .setSchedulerDetailsId(374)
                .setStartTime(1735730076000L)// appd
                .setEndTime(1751282950000L)//appd
                .setSchedulerTriggerTime(1716455057650L)
                .putMetadata("cronExpression","* * * * * ? *")
                .putMetadata("jobId","sd_374_sj_374_Thu May 23 14:34:17 IST 2024")
                .build();
        return scheduledJob;
    }

    private static ScheduledJobProtos.ScheduledJob getDtscheduler() {
        ScheduledJobProtos.Arguments arguments1 = ScheduledJobProtos.Arguments.newBuilder()
                .setName("queue_name")
                .setValue("DT-1_dynatrace")
                .setDefaultValue("DT-1_dynatrace").build();
        ScheduledJobProtos.Arguments arguments2 = ScheduledJobProtos.Arguments.newBuilder()
                .setName("connector_instance")
                .setValue("DT_11")
                .setDefaultValue("DT_11").build();
        //String chain_id = "Dynatrace_Topology";
        //String chain_id = "Dynatrace_KPIs";
        //String chain_id = "Dynatrace_Transactions";
        String chain_id = "Dynatrace_Events";
        ScheduledJobProtos.Arguments arguments3 = ScheduledJobProtos.Arguments.newBuilder()
                .setName("connector_chain")
                .setValue(chain_id)
                .setDefaultValue(chain_id).build();
        List<ScheduledJobProtos.Arguments> argumentsList = new ArrayList<>();
        argumentsList.add(arguments1);
        argumentsList.add(arguments2);
        argumentsList.add(arguments3);
        ScheduledJobProtos.ScheduledJob scheduledJob = ScheduledJobProtos.ScheduledJob.newBuilder()
                .setAccountIdentifier("dt_test")
                .addAllSchedulerArguments(argumentsList)
                .setSchedulerType("Recurring")
                .setTriggerSource("Scheduler-Service")
                //.setJobImplementationType("DynatraceTopology")
                //.setJobImplementationType("DynatraceKpi")
                //.setJobImplementationType("DynatraceTxn")
                .setJobImplementationType("DynatraceEvents")
                .setJobType("JobTypes")
                .setScheduledJobId(374)
                .setTriggerTime(1716455057650L)
                .setSchedulerDetailsId(374)
                .setStartTime(1716454980000L)
                .setEndTime(1716454980000L)
                .setSchedulerTriggerTime(1716455057650L)
                .putMetadata("cronExpression","* * * * * ? *")
                .putMetadata("jobId","sd_374_sj_374_Thu May 23 14:34:17 IST 2024")
                .build();
        return scheduledJob;
    }


    private static ScheduledJobProtos.ScheduledJob getRBscheduler() {
        ScheduledJobProtos.Arguments arguments1 = ScheduledJobProtos.Arguments.newBuilder()
                .setName("queue_name")
                .setValue("riverbed-connector-1")
                .setDefaultValue("riverbed-connector-1").build();
        ScheduledJobProtos.Arguments arguments2 = ScheduledJobProtos.Arguments.newBuilder()
                .setName("connector_instance")
                .setValue("RB-1_KPI")
                .setDefaultValue("RB-1_KPI").build();
        String chain_id = "Riverbed_KPIs";
        ScheduledJobProtos.Arguments arguments3 = ScheduledJobProtos.Arguments.newBuilder()
                .setName("connector_chain")
                .setValue(chain_id)
                .setDefaultValue(chain_id).build();
        List<ScheduledJobProtos.Arguments> argumentsList = new ArrayList<>();
        argumentsList.add(arguments1);
        argumentsList.add(arguments2);
        argumentsList.add(arguments3);
        ScheduledJobProtos.ScheduledJob scheduledJob = ScheduledJobProtos.ScheduledJob.newBuilder()
                .setAccountIdentifier("demo")
                .addAllSchedulerArguments(argumentsList)
                .setSchedulerType("Recurring")
                .setTriggerSource("Scheduler-Service")
                .setJobImplementationType("RiverbedKpi")
                .setJobType("JobTypes")
                .setScheduledJobId(374)
                .setTriggerTime(1716455057650L)
                .setSchedulerDetailsId(374)
                .setStartTime(1746680593000L)
                .setEndTime(1746680665000L)
                .setSchedulerTriggerTime(1716455057650L)
                .putMetadata("cronExpression","* * * * * ? *")
                .putMetadata("jobId","sd_374_sj_374_Thu May 23 14:34:17 IST 2024")
                .build();
        return scheduledJob;
    }

    private static ScheduledJobProtos.ScheduledJob getNewrelicScheduler() {
        ScheduledJobProtos.Arguments arguments1 = ScheduledJobProtos.Arguments.newBuilder()
                .setName("queue_name")
                .setValue("NR-1_NewRelic")
                .setDefaultValue("NR-1_NewRelic").build();
        ScheduledJobProtos.Arguments arguments2 = ScheduledJobProtos.Arguments.newBuilder()
                .setName("connector_instance")
                .setValue("NR-1")
                .setDefaultValue("NR-1").build();
        ScheduledJobProtos.Arguments arguments3 = ScheduledJobProtos.Arguments.newBuilder()
                .setName("connector_chain")
                //.setValue("Newrelic_KPIs")
                //.setDefaultValue("Newrelic_KPIs").build();
                .setValue("Newrelic_Transactions")
                .setDefaultValue("Newrelic_Transactions").build();
        List<ScheduledJobProtos.Arguments> argumentsList = new ArrayList<>();
        argumentsList.add(arguments1);
        argumentsList.add(arguments2);
        argumentsList.add(arguments3);
        ScheduledJobProtos.ScheduledJob scheduledJob = ScheduledJobProtos.ScheduledJob.newBuilder()
                .setAccountIdentifier("newrelic_test")
                .addAllSchedulerArguments(argumentsList)
                .setSchedulerType("Recurring")
                .setTriggerSource("Scheduler-Service")
                //.setJobImplementationType("NewrelicKpi")
                .setJobImplementationType("NewrelicTxn")
                .setJobType("JobTypes")
                .setScheduledJobId(374)
                .setTriggerTime(1716455057650L)
                .setSchedulerDetailsId(374)
                .setStartTime(1716454980000L)
                .setEndTime(1716454980000L)
                .setSchedulerTriggerTime(1716455057650L)
                .putMetadata("cronExpression","* * * * * ? *")
                .putMetadata("jobId","sd_374_sj_374_Thu May 23 14:34:17 IST 2024")
                .build();
        return scheduledJob;
    }

    private static ScheduledJobProtos.ScheduledJob getK8sScheduler() {
        ScheduledJobProtos.Arguments arguments1 = ScheduledJobProtos.Arguments.newBuilder()
                .setName("queue_name")
                .setValue("K8s-1_connector")
                .setDefaultValue("K8s-1_connector").build();
        ScheduledJobProtos.Arguments arguments2 = ScheduledJobProtos.Arguments.newBuilder()
                .setName("connector_instance")
                .setValue("NR-1")
                .setDefaultValue("NR-1").build();
        ScheduledJobProtos.Arguments arguments3 = ScheduledJobProtos.Arguments.newBuilder()
                .setName("connector_chain")
                //.setValue("k8s_test")
                //.setDefaultValue("k8s_test").build();
                .setValue("k8s_test1")
                .setDefaultValue("k8s_test1").build();
        List<ScheduledJobProtos.Arguments> argumentsList = new ArrayList<>();
        argumentsList.add(arguments1);
        argumentsList.add(arguments2);
        argumentsList.add(arguments3);
        ScheduledJobProtos.ScheduledJob scheduledJob = ScheduledJobProtos.ScheduledJob.newBuilder()
                .setAccountIdentifier("newrelic_test")
                .addAllSchedulerArguments(argumentsList)
                .setSchedulerType("Recurring")
                .setTriggerSource("Scheduler-Service")
                //.setJobImplementationType("NewrelicKpi")
                .setJobImplementationType("k8s1")
                .setJobType("JobTypes")
                .setScheduledJobId(374)
                .setTriggerTime(1716455057650L)
                .setSchedulerDetailsId(374)
                .setStartTime(1716454980000L)
                .setEndTime(1716454980000L)
                .setSchedulerTriggerTime(1716455057650L)
                .putMetadata("cronExpression","* * * * * ? *")
                .putMetadata("jobId","sd_374_sj_374_Thu May 23 14:34:17 IST 2024")
                .build();
        return scheduledJob;
    }


    private static ScheduledJobProtos.ScheduledJob getMultipleInstanceScheduler() {
        ScheduledJobProtos.Arguments arguments1 = ScheduledJobProtos.Arguments.newBuilder()
                .setName("queue_name")
                .setValue("multi-connector-1")
                .setDefaultValue("multi-connector-1").build();
        ScheduledJobProtos.Arguments arguments2 = ScheduledJobProtos.Arguments.newBuilder()
                .setName("connector_instance")
                //.setValue("DT-1_EVENT")
                //.setValue("DT-1_KPI")
                //.setValue("DT-1_TOPOLOGY")
                //.setValue("RB-1_KPI")
                .setValue("APPD-1_KPI")
                //.setValue("APPD-1_TXN")
                //.setValue("APPD-1_TOPOLOGY")
                //.setValue("APPD-1_EVENT")
                //.setValue("APPD-1_EVENT_RULE")

                //.setDefaultValue("DT-1_EVENT").build();
                //.setDefaultValue("DT-1_KPI").build();
                //.setDefaultValue("DT-1_TOPOLOGY").build();
                //.setDefaultValue("RB-1_KPI").build();
                .setDefaultValue("APPD-1_KPI").build();
                //.setDefaultValue("APPD-1_TXN").build();
                //.setDefaultValue("APPD-1_TOPOLOGY").build();
                //.setDefaultValue("APPD-1_EVENT").build();
                //.setDefaultValue("APPD-1_EVENT_RULE").build();
        //String chain_id = "Dynatrace_Events";
        //String chain_id = "Dynatrace_KPIs";
        //String chain_id = "Dynatrace_Topology";
        //String chain_id = "Riverbed_KPIs";
        String chain_id = "Appdynamics_KPIs";
        //String chain_id = "Appdynamics_Transactions";
        //String chain_id = "Appdynamics_Topology";
        //String chain_id = "Appdynamics_Events";
        //String chain_id = "Appdynamics_Rule_Violation_Events";
        ScheduledJobProtos.Arguments arguments3 = ScheduledJobProtos.Arguments.newBuilder()
                .setName("connector_chain")
                .setValue(chain_id)
                .setDefaultValue(chain_id).build();
        List<ScheduledJobProtos.Arguments> argumentsList = new ArrayList<>();
        argumentsList.add(arguments1);
        argumentsList.add(arguments2);
        argumentsList.add(arguments3);
        ScheduledJobProtos.ScheduledJob scheduledJob = ScheduledJobProtos.ScheduledJob.newBuilder()
                .setAccountIdentifier("multiple_chains")
                .addAllSchedulerArguments(argumentsList)
                .setSchedulerType("Recurring")
                .setTriggerSource("Scheduler-Service")
                .setJobImplementationType("AppdynamicsKpi")
                //.setJobImplementationType("RiverbedKpi")
                //.setJobImplementationType("DynatraceKpi")
                .setJobType("JobTypes")
                .setScheduledJobId(374)
                .setTriggerTime(1746680593000L)
                .setSchedulerDetailsId(374)
                .setStartTime(1735730076000L)// appd
                .setEndTime(1751282950000L)//appd
                //.setStartTime(1702605600000L)
                //.setEndTime(1703055642000L)
                .setSchedulerTriggerTime(1703055642000L)
                .putMetadata("cronExpression","* * * * * ? *")
                .putMetadata("jobId","sd_374_sj_374_Thu June 5 10:34:17 IST 2024")
                .build();
        return scheduledJob;
    }
}
