# suppress inspection "UnusedProperty" for whole file
# ==========================================================
# RabbitMQ Configuration
# ==========================================================
spring.rabbitmq.addresses=**************:5671
spring.rabbitmq.username=guest
spring.rabbitmq.password=Z3Vlc3Q=
spring.rabbitmq.ssl.enabled=true
spring.rabbitmq.ssl.algorithm=TLSv1.2

connector.message.queue.name=riverbed-connector-1
anomaly.queue.name=anomaly-event-action-messages
violated.queue.name=violated-event-action-messages
forensic.output.queue.name=forensic-output-messages

# ==========================================================
# Mysql Database Configuration
# ==========================================================
#spring.datasource.url=************************************************************************************************************************************************************
spring.datasource.url=************************************************************************************************************************************************************
spring.datasource.username=dbadmin
spring.datasource.password=cm9vdEAxMjM=
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.minimum.idle.connections=100
spring.datasource.maximum.pool.size=200
spring.datasource.connection.timeout=30000
spring.datasource.connection.idle.timeout=600000
spring.datasource.max.life.time=1800000

# ==========================================================
# Redis Configuration
# ==========================================================
#spring.redis.cluster.nodes=192.168.13.167:7001,192.168.13.167:7002,192.168.13.167:7003,192.168.13.167:7004,192.168.13.167:7005,192.168.13.167:7006
spring.redis.cluster.nodes=**************:5001,**************:5002,**************:5003,**************:5004,**************:5005,**************:5006
spring.redis.ssl=true
spring.redis.username=
spring.redis.password=cmVkaXNAMTIz
spring.redis.cluster.mode=true
spring.redis.max.idle.connections=500
spring.redis.min.idle.connections=500
spring.redis.max.total.connections=500
spring.redis.max.wait.secs=20
spring.redis.share.native.connection=true

# ==========================================================
# Health Metrics
# ==========================================================
health.metrics.update.interval.milliseconds=60000
health.metrics.log.interval.milliseconds=10000
management.endpoints.web.exposure.include=*
management.endpoints.jmx.exposure.include=*
management.endpoint.health.enabled=true
management.endpoints.web.base-path=/measure
management.server.port=8989
spring.jmx.enabled=true

# ==========================================================
# Undertow HTTP2 Configuration
# ==========================================================
#server.http2.enabled=false
server.port=7443
#server.ssl.enabled-protocols=TLSv1.3
#server.ssl.key-store=/opt/heal-motadata-connector/appnomic-keystore.jks
#server.ssl.key-store-password=
#server.ssl.trust-store=/opt/heal-motadata-connector/appnomic-truststore.jks
#server.ssl.trust-store-password=

# ==========================================================
# Misc Configuration
# ==========================================================
connector.name=riverbed

## ==========================================================
## keycloak Configuration
## ==========================================================
keycloak.host=**************
keycloak.port=8443
keycloak.username=appsoneadmin
keycloak.password=QXBwc29uZUAxMjM=