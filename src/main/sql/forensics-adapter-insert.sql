# Forensics Extractor Configuration
# This file contains SQL statements to configure the Riverbed Forensics Extractor

# Insert connector worker for Forensics Extractor
INSERT INTO appsone.connector_workers (`class_path`, `worker_type_id`) 
VALUES ('com.heal.riverbed.extractor.RiverbedForensicsExtractor', (select id from mst_sub_type where name='Extractor'));

# Insert connector worker for Forensics Transformer (using HealForensicsTransformer)
INSERT INTO appsone.connector_workers (`class_path`, `worker_type_id`) 
VALUES ('com.heal.etladapter.transformers.HealForensicsTransformer', (select id from mst_sub_type where name='Transformer'));

# Insert connector worker for Forensics Loader (using existing HealKPIHttpLoader)
INSERT INTO appsone.connector_workers (`class_path`, `worker_type_id`) 
VALUES ('com.heal.etladapter.loaders.HealKPIHttpLoader', (select id from mst_sub_type where name='Loader'));

# Create connector chain for Forensics
INSERT INTO appsone.connector_chains (`chain_identifier`, `status`, `created_time`, `updated_time`, `user_details_id`) 
VALUES ('Riverbed_Forensics', 1, '2025-03-13 00:00:00', '2025-03-13 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');

# Map workers to the Forensics chain
INSERT INTO appsone.connector_chain_worker_mapping (`connector_chain_id`, `connector_worker_id`, `status`, `re_initialize`, `order`, `created_time`, `updated_time`, `user_details_id`) 
VALUES ((select id from appsone.connector_chains where chain_identifier='Riverbed_Forensics'), 
        (select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedForensicsExtractor'), 
        1, 1, 1, '2025-03-13 00:00:00', '2025-03-13 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');

INSERT INTO appsone.connector_chain_worker_mapping (`connector_chain_id`, `connector_worker_id`, `status`, `re_initialize`, `order`, `created_time`, `updated_time`, `user_details_id`) 
VALUES ((select id from appsone.connector_chains where chain_identifier='Riverbed_Forensics'), 
        (select id from appsone.connector_workers where class_path='com.heal.etladapter.transformers.HealForensicsTransformer'), 
        1, 1, 2, '2025-03-13 00:00:00', '2025-03-13 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');

INSERT INTO appsone.connector_chain_worker_mapping (`connector_chain_id`, `connector_worker_id`, `status`, `re_initialize`, `order`, `created_time`, `updated_time`, `user_details_id`) 
VALUES ((select id from appsone.connector_chains where chain_identifier='Riverbed_Forensics'), 
        (select id from appsone.connector_workers where class_path='com.heal.etladapter.loaders.HealKPIHttpLoader'), 
        1, 1, 3, '2025-03-13 00:00:00', '2025-03-13 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');

# Configure parameters for Forensics Extractor
insert into appsone.connector_worker_parameters (`name`, `value`, `connector_worker_id`)  
values('domain','riverbed',(select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedForensicsExtractor'));

insert into appsone.connector_worker_parameters (`name`, `value`, `connector_worker_id`)  
values('riverbed.forensics.request.payload', 
'{"criteria":{"time_frame":{"start":${startTime},"end":${endTime},"resolution":"${resolution}"},"queries":[{"sort_column":33,"realm":"forensics_summary","traffic_expression":"forensics ${forensicsType}","group_by":"session","limit":${limit},"columns":[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20]}]},"template_id":185}',
(select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedForensicsExtractor'));

insert into appsone.connector_worker_parameters (`name`, `value`, `connector_worker_id`)  
values('riverbed.url','https://riverbed-api-endpoint.com/api/forensics',
(select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedForensicsExtractor'));

insert into appsone.connector_worker_parameters (`name`, `value`, `connector_worker_id`)  
values('riverbed.authorization','Bearer your-auth-token-here',
(select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedForensicsExtractor'));

insert into appsone.connector_worker_parameters (`name`, `value`, `connector_worker_id`)  
values('riverbed.entity.types','NETWORK_TRAFFIC,SECURITY_EVENTS,APPLICATION_FLOWS',
(select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedForensicsExtractor'));

insert into appsone.connector_worker_parameters (`name`, `value`, `connector_worker_id`)  
values('forensics.types','network_traffic,security_events,application_flows',
(select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedForensicsExtractor'));

# Command Request Configuration
insert into appsone.connector_worker_parameters (`name`, `value`, `connector_worker_id`)  
values('supervisor.identifier','riverbed-forensics-supervisor',
(select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedForensicsExtractor'));

insert into appsone.connector_worker_parameters (`name`, `value`, `connector_worker_id`)  
values('agent.identifier','riverbed-forensics-agent',
(select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedForensicsExtractor'));

insert into appsone.connector_worker_parameters (`name`, `value`, `connector_worker_id`)  
values('agent.type','RIVERBED_FORENSICS',
(select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedForensicsExtractor'));

insert into appsone.connector_worker_parameters (`name`, `value`, `connector_worker_id`)  
values('trigger.source','SCHEDULED',
(select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedForensicsExtractor'));

# Forensics-specific type configurations
INSERT INTO `appsone`.`connector_worker_parameters` (`name`, `value`, `connector_worker_id`) 
VALUES ('forensics_network_traffic_limit', '1000', (select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedForensicsExtractor'));

INSERT INTO `appsone`.`connector_worker_parameters` (`name`, `value`, `connector_worker_id`) 
VALUES ('forensics_network_traffic_resolution', '1min', (select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedForensicsExtractor'));

INSERT INTO `appsone`.`connector_worker_parameters` (`name`, `value`, `connector_worker_id`) 
VALUES ('forensics_network_traffic_filters', 'protocol=TCP,UDP', (select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedForensicsExtractor'));

INSERT INTO `appsone`.`connector_worker_parameters` (`name`, `value`, `connector_worker_id`) 
VALUES ('forensics_security_events_limit', '500', (select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedForensicsExtractor'));

INSERT INTO `appsone`.`connector_worker_parameters` (`name`, `value`, `connector_worker_id`) 
VALUES ('forensics_security_events_resolution', '1min', (select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedForensicsExtractor'));

INSERT INTO `appsone`.`connector_worker_parameters` (`name`, `value`, `connector_worker_id`) 
VALUES ('forensics_security_events_filters', 'severity=HIGH,CRITICAL', (select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedForensicsExtractor'));

INSERT INTO `appsone`.`connector_worker_parameters` (`name`, `value`, `connector_worker_id`) 
VALUES ('forensics_application_flows_limit', '750', (select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedForensicsExtractor'));

INSERT INTO `appsone`.`connector_worker_parameters` (`name`, `value`, `connector_worker_id`) 
VALUES ('forensics_application_flows_resolution', '5min', (select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedForensicsExtractor'));

INSERT INTO `appsone`.`connector_worker_parameters` (`name`, `value`, `connector_worker_id`) 
VALUES ('forensics_application_flows_filters', 'application=HTTP,HTTPS,FTP', (select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedForensicsExtractor'));

# Configure parameters for Forensics Transformer (HealForensicsTransformer)
insert into appsone.connector_worker_parameters (`name`, `value`, `connector_worker_id`)  
values('domain','riverbed',(select id from appsone.connector_workers where class_path='com.heal.etladapter.transformers.HealForensicsTransformer' and id not in (select connector_worker_id from appsone.connector_chain_worker_mapping where connector_chain_id != (select id from appsone.connector_chains where chain_identifier='Riverbed_Forensics'))));

# Configure parameters for Forensics Loader (reusing HealKPIHttpLoader)
insert into appsone.connector_worker_parameters (`name`, `value`, `connector_worker_id`)  
values('data.receiver.kpi.endpoint','https://haproxy.appnomic:9998/raw-agents-forensics-data',(select id from appsone.connector_workers where class_path='com.heal.etladapter.loaders.HealKPIHttpLoader' and id not in (select connector_worker_id from appsone.connector_chain_worker_mapping where connector_chain_id = (select id from appsone.connector_chains where chain_identifier='Riverbed_KPIs'))));

insert into appsone.connector_worker_parameters (`name`, `value`, `connector_worker_id`)  
values('domain','riverbed',(select id from appsone.connector_workers where class_path='com.heal.etladapter.loaders.HealKPIHttpLoader' and id not in (select connector_worker_id from appsone.connector_chain_worker_mapping where connector_chain_id = (select id from appsone.connector_chains where chain_identifier='Riverbed_KPIs'))));

# Sample forensics data mapping configuration
INSERT INTO `appsone`.`connector_worker_parameters` (`name`, `value`, `connector_worker_id`) 
VALUES ('forensics.column.mapping', 
'{"1":"Client IP","2":"Server IP","3":"Client Port","4":"Server Port","5":"Protocol","6":"Start Time","7":"Bytes","8":"Packets","9":"Application","10":"User","11":"Host","12":"Location","13":"Response Time","14":"Transaction ID","15":"HTTP Method","16":"URL","17":"Status Code","18":"User Agent","19":"Referrer","20":"Severity"}', 
(select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedForensicsExtractor'));
