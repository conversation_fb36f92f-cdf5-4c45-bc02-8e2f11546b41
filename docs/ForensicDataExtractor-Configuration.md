# ForensicDataExtractor Configuration Guide

## Overview

The `ForensicDataExtractor` is designed to extract forensic data from database sources and create `ForensicOutput` objects containing `CommandRequest` protobuf messages and lists of `ForensicItem` objects (specifically `DomainKpi` instances). This extractor integrates seamlessly with the `HealForensicsTransformer` to provide end-to-end forensic data processing.

## Required Configuration Parameters

### Core Database Configuration

#### 1. SQL Query Configuration
```properties
forensic.sql.query=SELECT kpi_name, kpi_value, service_name, domain_instance_id, collection_interval, lower_threshold, upper_threshold, kpi_type, heal_instance, entry_metric, exit_metric FROM forensic_kpi_data WHERE collection_time BETWEEN ? AND ? ORDER BY collection_time DESC
```
- **Purpose**: SQL query to extract forensic KPI data from the database
- **Required**: Yes
- **Parameters**: Query should accept two timestamp parameters (from, to)
- **Expected Columns**:
  - `kpi_name` (String): Name of the KPI
  - `kpi_value` (Double): Numeric value of the KPI
  - `service_name` (String): Service associated with the KPI
  - `domain_instance_id` (String): Domain instance identifier
  - `collection_interval` (Integer): Collection interval in seconds
  - `lower_threshold` (Timestamp): Lower time threshold
  - `upper_threshold` (Timestamp): Upper time threshold
  - `kpi_type` (String): Type of KPI (must match KpiType enum values)
  - `heal_instance` (String): Heal instance identifier
  - `entry_metric` (Double): Entry metric value
  - `exit_metric` (Double): Exit metric value

### Command Request Configuration

#### 2. Agent Configuration
```properties
forensic.agent.identifier=forensic-agent-001
forensic.agent.type=FORENSIC_AGENT
forensic.supervisor.identifier=forensic-supervisor-001
```
- **forensic.agent.identifier**: Unique identifier for the forensic agent
- **forensic.agent.type**: Type of agent (default: FORENSIC_AGENT)
- **forensic.supervisor.identifier**: Supervisor identifier for command requests

#### 3. Command Configuration
```properties
forensic.command.type=FORENSIC_COLLECTION
forensic.trigger.source=SCHEDULED
```
- **forensic.command.type**: Type of forensic command (default: FORENSIC_COLLECTION)
- **forensic.trigger.source**: Source that triggered the forensic collection (default: SCHEDULED)

## Optional Configuration Parameters

### Performance Configuration

#### 4. Batch Processing
```properties
forensic.batch.size=100
```
- **Purpose**: Number of records to process in each batch
- **Default**: 100
- **Range**: 1-1000 (recommended)

### Data Filtering Configuration

#### 5. Service Filtering
```properties
forensic.kpi.filter.services=web-service,api-service,database-service
```
- **Purpose**: Comma-separated list of services to include in forensic data
- **Optional**: If not specified, all services are included
- **Example**: Only KPIs from specified services will be extracted

#### 6. Threshold Filtering
```properties
forensic.kpi.filter.threshold.min=0.0
forensic.kpi.filter.threshold.max=100.0
```
- **forensic.kpi.filter.threshold.min**: Minimum KPI value threshold
- **forensic.kpi.filter.threshold.max**: Maximum KPI value threshold
- **Purpose**: Filter KPIs based on their numeric values
- **Optional**: If not specified, no threshold filtering is applied

## Database Connection Configuration

The extractor uses the standard Spring JdbcTemplate configuration. Ensure the following database properties are configured:

### Database Properties
```properties
# Database Connection
datasource.url=***************************************
datasource.username=forensic_user
datasource.password=forensic_password
datasource.driver.class.name=com.mysql.cj.jdbc.Driver

# Connection Pool Settings
datasource.minimum.idle.connections=5
datasource.maximum.pool.size=20
datasource.connection.timeout=30000
datasource.connection.idle.timeout=600000
datasource.max.life.time=1800000
```

## Sample Database Schema

### Forensic KPI Data Table
```sql
CREATE TABLE forensic_kpi_data (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    kpi_name VARCHAR(255) NOT NULL,
    kpi_value DOUBLE NOT NULL,
    service_name VARCHAR(255) NOT NULL,
    domain_instance_id VARCHAR(255) NOT NULL,
    collection_interval INT NOT NULL DEFAULT 300,
    lower_threshold TIMESTAMP NOT NULL,
    upper_threshold TIMESTAMP NOT NULL,
    kpi_type ENUM('PERFORMANCE', 'AVAILABILITY', 'ERROR_RATE', 'THROUGHPUT') NOT NULL,
    heal_instance VARCHAR(255),
    entry_metric DOUBLE,
    exit_metric DOUBLE,
    collection_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_collection_time (collection_time),
    INDEX idx_service_name (service_name),
    INDEX idx_kpi_name (kpi_name),
    INDEX idx_domain_instance (domain_instance_id)
);
```

### Sample Data
```sql
INSERT INTO forensic_kpi_data (
    kpi_name, kpi_value, service_name, domain_instance_id, 
    collection_interval, lower_threshold, upper_threshold, 
    kpi_type, heal_instance, entry_metric, exit_metric
) VALUES 
('CPU_UTILIZATION', 85.5, 'web-service', 'web-01', 300, 
 '2024-01-01 10:00:00', '2024-01-01 10:05:00', 
 'PERFORMANCE', 'heal-instance-01', 80.0, 90.0),
('MEMORY_USAGE', 72.3, 'api-service', 'api-01', 300,
 '2024-01-01 10:00:00', '2024-01-01 10:05:00',
 'PERFORMANCE', 'heal-instance-01', 70.0, 75.0),
('ERROR_RATE', 2.1, 'database-service', 'db-01', 300,
 '2024-01-01 10:00:00', '2024-01-01 10:05:00',
 'ERROR_RATE', 'heal-instance-01', 1.5, 2.5);
```

## Complete Configuration Example

### Application Properties
```properties
# Forensic Extractor Configuration
forensic.sql.query=SELECT kpi_name, kpi_value, service_name, domain_instance_id, collection_interval, lower_threshold, upper_threshold, kpi_type, heal_instance, entry_metric, exit_metric FROM forensic_kpi_data WHERE collection_time BETWEEN ? AND ? ORDER BY collection_time DESC
forensic.agent.identifier=forensic-agent-prod-001
forensic.agent.type=FORENSIC_AGENT
forensic.supervisor.identifier=forensic-supervisor-prod-001
forensic.command.type=FORENSIC_COLLECTION
forensic.trigger.source=SCHEDULED
forensic.batch.size=50

# Optional Filters
forensic.kpi.filter.services=web-service,api-service,database-service
forensic.kpi.filter.threshold.min=0.0
forensic.kpi.filter.threshold.max=100.0

# Database Configuration
datasource.url=************************************************************
datasource.username=forensic_reader
datasource.password=${FORENSIC_DB_PASSWORD}
datasource.driver.class.name=com.mysql.cj.jdbc.Driver
datasource.minimum.idle.connections=5
datasource.maximum.pool.size=20
datasource.connection.timeout=30000
```

### Chain Configuration (SQL Insert)
```sql
-- Insert extractor worker
INSERT INTO appsone.adapter_chain_worker (class_path, is_initialized) 
VALUES ('com.heal.etladapter.extractors.ForensicDataExtractor', 0);

-- Insert configuration parameters
INSERT INTO appsone.worker_parameters (name, value, adapter_chain_worker_id) VALUES
('forensic.sql.query', 'SELECT kpi_name, kpi_value, service_name, domain_instance_id, collection_interval, lower_threshold, upper_threshold, kpi_type, heal_instance, entry_metric, exit_metric FROM forensic_kpi_data WHERE collection_time BETWEEN ? AND ? ORDER BY collection_time DESC', 
 (SELECT id FROM appsone.adapter_chain_worker WHERE class_path='com.heal.etladapter.extractors.ForensicDataExtractor')),
('forensic.agent.identifier', 'forensic-agent-prod-001',
 (SELECT id FROM appsone.adapter_chain_worker WHERE class_path='com.heal.etladapter.extractors.ForensicDataExtractor')),
('forensic.supervisor.identifier', 'forensic-supervisor-prod-001',
 (SELECT id FROM appsone.adapter_chain_worker WHERE class_path='com.heal.etladapter.extractors.ForensicDataExtractor')),
('forensic.command.type', 'FORENSIC_COLLECTION',
 (SELECT id FROM appsone.adapter_chain_worker WHERE class_path='com.heal.etladapter.extractors.ForensicDataExtractor')),
('forensic.batch.size', '50',
 (SELECT id FROM appsone.adapter_chain_worker WHERE class_path='com.heal.etladapter.extractors.ForensicDataExtractor'));
```

## Integration with HealForensicsTransformer

The `ForensicDataExtractor` is designed to work seamlessly with the `HealForensicsTransformer`:

1. **Extractor Output**: Produces `ForensicOutput` objects
2. **Transformer Input**: Consumes `ForensicOutput` objects
3. **Data Flow**: ForensicOutput → JSON Serialization → GZIP Compression → Base64 Encoding → A1Event

### Complete ETL Chain Configuration
```java
// Example chain setup
AdapterChain forensicChain = new AdapterChain();
forensicChain.setChainId("Forensic Data Processing Chain");
forensicChain.setExtractor(new ForensicDataExtractor());
forensicChain.setTransformer(new HealForensicsTransformer());
forensicChain.setLoader(new HealForensicLoader());
```

## Monitoring and Health Metrics

The extractor provides the following health metrics:
- `ForensicExtractorInitializationError`: Initialization failures
- `ForensicExtractorError`: Runtime extraction errors
- Received count metrics for monitoring data volume
- Database connection health monitoring

## Troubleshooting

### Common Issues

1. **Database Connection Failures**
   - Verify database credentials and connectivity
   - Check connection pool configuration
   - Ensure database schema exists

2. **SQL Query Errors**
   - Validate SQL syntax and column names
   - Ensure proper parameter binding
   - Check data types match expected schema

3. **Configuration Errors**
   - Verify all required parameters are provided
   - Check parameter value formats
   - Validate enum values (KpiType)

4. **Performance Issues**
   - Adjust batch size based on data volume
   - Optimize SQL query with proper indexes
   - Monitor connection pool utilization

### Logging Configuration
```properties
# Enable debug logging for forensic extractor
logging.level.com.heal.etladapter.extractors.ForensicDataExtractor=DEBUG
logging.level.com.heal.etladapter.transformers.HealForensicsTransformer=DEBUG
```
