# Riverbed Forensics Extractor

## Overview

The `RiverbedForensicsExtractor` is a specialized data extraction component designed to collect forensics data from Riverbed network monitoring systems. It is specifically designed to work with the `HealForensicsTransformer` and produces `ForensicOutput` objects containing `ForensicItem` collections.

## Architecture

The extractor extends `HttpExtractor<Object, ForensicOutput>` and integrates with the Heal ETL adapter framework to:

1. **Extract** forensics data from Riverbed APIs
2. **Transform** the data using the `HealForensicsTransformer`
3. **Load** the data using the existing `HealKPIHttpLoader`

## Key Features

- **HTTP-based data extraction** from Riverbed forensics APIs
- **Command Request integration** for forensics collection workflows
- **Multiple forensics types** support (network traffic, security events, application flows)
- **XML response parsing** using the existing XmlParser with forensics-specific methods
- **Error handling and logging** with comprehensive monitoring
- **Health metrics integration** for operational monitoring
- **Flexible parameter configuration** for different forensics scenarios

## Data Flow

```
Riverbed API → RiverbedForensicsExtractor → ForensicOutput → HealForensicsTransformer → A1Event → HealKPIHttpLoader
```

## Configuration Parameters

### Required Parameters

| Parameter | Description | Example |
|-----------|-------------|---------|
| `riverbed.url` | Riverbed API endpoint URL | `https://riverbed-api.com/api/forensics` |
| `riverbed.forensics.request.payload` | JSON template for forensics requests | See payload template below |
| `riverbed.authorization` | Authorization token | `Bearer your-token-here` |
| `riverbed.entity.types` | Entity types to monitor | `NETWORK_TRAFFIC,SECURITY_EVENTS` |
| `domain` | Domain identifier | `riverbed` |

### Optional Parameters

| Parameter | Description | Default |
|-----------|-------------|---------|
| `forensics.types` | Forensics types to process | `network_traffic,security_events,application_flows` |
| `supervisor.identifier` | Supervisor identifier for command requests | `riverbed-forensics-supervisor` |
| `agent.identifier` | Agent identifier for command requests | `riverbed-forensics-agent` |
| `agent.type` | Agent type for command requests | `RIVERBED_FORENSICS` |
| `trigger.source` | Trigger source for command requests | `SCHEDULED` |
| `forensics_{type}_limit` | Result limit per forensics type | `1000` |
| `forensics_{type}_resolution` | Time resolution | `1min` |
| `forensics_{type}_filters` | Type-specific filters | varies by type |

## Payload Template

The forensics request payload template supports the following placeholders:

```json
{
  "criteria": {
    "time_frame": {
      "start": "${startTime}",
      "end": "${endTime}",
      "resolution": "${resolution}"
    },
    "queries": [{
      "sort_column": 33,
      "realm": "forensics_summary",
      "traffic_expression": "forensics ${forensicsType}",
      "group_by": "session",
      "limit": ${limit},
      "columns": [1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20]
    }]
  },
  "template_id": 185
}
```

### Placeholder Variables

- `${startTime}` - Unix timestamp for query start time
- `${endTime}` - Unix timestamp for query end time
- `${resolution}` - Time resolution (e.g., "1min", "5min")
- `${forensicsType}` - Type of forensics data to collect
- `${limit}` - Maximum number of results
- `${filters}` - Type-specific filters

## Forensics Types

The extractor supports multiple forensics types:

### Network Traffic
- **Type**: `network_traffic`
- **Description**: Network flow and traffic analysis data
- **Default Filters**: `protocol=TCP,UDP`
- **Default Limit**: 1000

### Security Events
- **Type**: `security_events`
- **Description**: Security incidents and threat detection data
- **Default Filters**: `severity=HIGH,CRITICAL`
- **Default Limit**: 500

### Application Flows
- **Type**: `application_flows`
- **Description**: Application-level traffic and performance data
- **Default Filters**: `application=HTTP,HTTPS,FTP`
- **Default Limit**: 750

## Data Structures

### ForensicOutput
The main output object containing:
- `CommandRequest` - Command metadata for the forensics collection
- `List<ForensicItem>` - Collection of forensics data items

### ForensicItem
Individual forensics data item containing:
- `itemId` - Unique identifier for the forensics session
- `itemType` - Type of forensics data (network_traffic, security_events, etc.)
- `timestamp` - Collection timestamp
- `data` - Map containing forensics data fields

### Forensics Data Fields
Each ForensicItem contains a data map with fields such as:
- **Network**: sourceIp, destinationIp, sourcePort, destinationPort, protocol
- **Traffic**: bytesTransferred, packetsTransferred, responseTime, networkLatency
- **Application**: applicationName, httpMethod, httpUrl, httpStatusCode, userAgent
- **Security**: threatLevel, riskScore, analysisResult, severity, eventType
- **Metadata**: sessionId, userName, deviceName, location, timestamp

## Column Mapping

The extractor maps Riverbed column names to standardized forensics fields:

```json
{
  "1": "Client IP",
  "2": "Server IP", 
  "3": "Client Port",
  "4": "Server Port",
  "5": "Protocol",
  "6": "Start Time",
  "7": "Bytes",
  "8": "Packets",
  "9": "Application",
  "10": "User",
  "11": "Host",
  "12": "Location",
  "13": "Response Time",
  "14": "Transaction ID",
  "15": "HTTP Method",
  "16": "URL",
  "17": "Status Code",
  "18": "User Agent",
  "19": "Referrer",
  "20": "Severity"
}
```

## Command Request Integration

The extractor creates `CommandRequest` objects for integration with the forensics workflow:

- **Command Type**: `FORENSICS_COLLECTION`
- **Command**: `collect_forensics_data`
- **Job ID**: Auto-generated UUID
- **Supervisor/Agent**: Configurable identifiers
- **Trigger Source**: Configurable (default: SCHEDULED)

## Error Handling

The extractor includes comprehensive error handling:

- **Configuration validation** during initialization
- **API response validation** during extraction
- **Health metrics tracking** for monitoring
- **Graceful degradation** when individual forensics types fail

## Health Metrics

The following health metrics are tracked:

- `RiverbedForensicsExtractorConfigurationError` - Configuration issues
- `RiverbedForensicsExtractorErrors` - Runtime extraction errors
- `RiverbedForensicsExtractorInitializationError` - Initialization failures

## Database Configuration

Use the provided SQL script `forensics-adapter-insert.sql` to configure:

1. Connector workers for extractor, transformer, and loader
2. Connector chain for forensics processing
3. Worker parameters for configuration
4. Chain mappings for proper execution order

## Integration with HealForensicsTransformer

The extractor produces `ForensicOutput` objects that are consumed by the `HealForensicsTransformer`:

1. **ForensicOutput** → **HealForensicsTransformer** → **List<A1Event>**
2. **Compression**: Forensics data is JSON serialized, GZIP compressed, and Base64 encoded
3. **Command Response**: Wrapped in CommandResponse protobuf messages
4. **A1Event**: Final output as A1Event with type "FORENSIC_OUTPUT"

## Usage Example

```java
// The extractor is automatically instantiated by the Spring framework
// Configuration is loaded from database parameters
// Execution is triggered by the ETL framework

// Example of manual usage (for testing):
RiverbedForensicsExtractor extractor = new RiverbedForensicsExtractor();
extractor.initialize(); // Loads configuration
ForensicOutput forensicsData = extractor.extract(startTime, endTime, inputData);
```

## Testing

The `RiverbedForensicsExtractorTest` class provides comprehensive unit tests covering:

- Successful initialization
- Configuration validation
- Error handling scenarios
- Parameter defaults
- Data extraction flows
- Command request creation
- Multiple forensics types processing

Run tests using:
```bash
mvn test -Dtest=RiverbedForensicsExtractorTest
```

## Monitoring and Logging

The extractor provides detailed logging at multiple levels:

- **TRACE**: Detailed execution flow and data
- **DEBUG**: Configuration and processing details
- **INFO**: High-level operation status
- **ERROR**: Failures and exceptions

Logs include correlation IDs for tracking across the forensics collection workflow.

## Performance Considerations

- **Configurable limits** prevent excessive data retrieval
- **Type-specific processing** allows parallel collection
- **Connection pooling** for HTTP requests
- **Memory-efficient** streaming where possible
- **Compression** reduces data transfer overhead

## Security

- **Token-based authentication** for API access
- **Secure parameter storage** in database
- **Input validation** for all configuration parameters
- **Error message sanitization** to prevent information leakage
- **Forensics data encryption** in transit and at rest
